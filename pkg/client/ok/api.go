package ok

import (
	"GoTrader/pkg/api"
	okapi "GoTrader/pkg/api/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	okUtils "GoTrader/pkg/utils/ok"
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
)

// OkAPIClient 传统账户客户端
type OkAPIClient struct {
	url                 string             // API URL
	httpClient          *http.Client       // http连接实例
	fundHistLimiter     *utils.FreqLimiter // 历史资金费请求限流器
	tiersRequestLimiter *utils.FreqLimiter // 仓位档位请求限流器
	logger              *slog.Logger       // 日志对象
}

// ok订单请求
type OrderRequest struct {
	InstId  string `json:"instId,omitempty"`
	TdMode  string `json:"tdMode,omitempty"`
	PosSide string `json:"posSide,omitempty"`
	Side    string `json:"side,omitempty"`
	OrdType string `json:"ordType,omitempty"`
	Size    string `json:"sz,omitempty"`
	Price   string `json:"px,omitempty"`
	ClOrdId string `json:"clOrdId,omitempty"`
	TgtCcy  string `json:"tgtCcy,omitempty"`
}

type BrokerWebSocketOk struct {
	Arg Arg `json:"arg"` // 请求参数
}

// Arg 表示请求中的参数部分
type Arg struct {
	Channel string `json:"channel"` // 通道
	Uid     string `json:"uid"`     // 用户ID
}

// 创建客户端
func NewAPIClient(url string, httpClient *http.Client, logger *slog.Logger) *OkAPIClient {
	logger = logger.With("client", "ok")
	c := &OkAPIClient{
		url:                 url,
		httpClient:          httpClient,
		logger:              logger,
		fundHistLimiter:     utils.NewFreqLimiter(4, 1*time.Second),
		tiersRequestLimiter: utils.NewFreqLimiter(4, 1*time.Second),
	}
	return c
}

// 更新broker信息
func (c *OkAPIClient) UpdateBroker(b *broker.Broker) (err error) {
	// 更新Broker
	if err = c.updateBrokerAccountType(b); err != nil {
		return err
	}
	if err = c.updateBrokerCtValues(b); err != nil {
		return err
	}
	if err = c.updateBrokerBalance(b); err != nil {
		return err
	}
	if err = c.updateBrokerPosition(b); err != nil {
		return err
	}
	if err = c.updateFuturesDataComm(b); err != nil {
		return err
	}
	if err = c.updateSpotDataComm(b); err != nil {
		return err
	}
	return nil
}

// 更新broker期货挂单
func (c *OkAPIClient) UpdateBrokerUMPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Futures {
			if err = c.queryUMPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新broker杠杆挂单
func (c *OkAPIClient) UpdateBrokerMarginPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Spot {
			if err = c.queryMarginPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新期货数据
func (c *OkAPIClient) UpdateFuturesDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateFuturesDataInfo(ds); err != nil {
		return err
	}
	if err = c.updateFuturesFunding(ds); err != nil {
		return err
	}
	c.updateDepthMaxSize(ds)
	return nil
}

// 更新现货数据
func (c *OkAPIClient) UpdateSpotDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateSpotDataInfo(ds); err != nil {
		return err
	}
	c.updateDepthMaxSize(ds)
	return nil
}

// 发送http请求
func (c *OkAPIClient) HttpDo(req *http.Request, logger *slog.Logger) ([]byte, error) {
	// 记录响应耗时
	pre := time.Now()
	resp, err := c.httpClient.Do(req)
	respTime := time.Since(pre)
	if err != nil {
		logger.Error("Error sending request: %v", "error", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 正确读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Error reading response body", "error", err)
		return nil, fmt.Errorf("status: %d body: %s", resp.StatusCode, string(body))
	}
	logger.Info("Response", "body", utils.TruncateString(string(body), 500), "statusCode", resp.StatusCode, "respTime", respTime.String())

	// 检查状态码
	if resp.StatusCode != 200 {
		return body, fmt.Errorf("request failed with status code %d, body: %s", resp.StatusCode, body)
	}

	return body, nil
}

// 发送期货api请求
func (c *OkAPIClient) SendFutures(data map[string]any, rURL api.APIMethod, b *broker.Broker) (response []byte, err error) {
	return c.sendPublic(data, rURL, b)
}

// 发送现货api请求
func (c *OkAPIClient) SendSpot(data map[string]any, rURL api.APIMethod, b *broker.Broker) (response []byte, err error) {
	return c.sendPublic(data, rURL, b)
}

func (c *OkAPIClient) sendPublic(data map[string]any, rURL api.APIMethod, b *broker.Broker) (response []byte, err error) {
	// 解析基础 URL
	fullURL := utils.URLJoin(c.url, rURL.Path)
	// 转换为 url.Values
	formData := utils.MapToURLValues(data)
	logger := c.logger.With("url", fullURL).With("data", formData)
	// 发送请求
	req, err := http.NewRequest(rURL.Method, fullURL+"?"+formData, nil)
	if err != nil {
		logger.Error("Error creating request: %v", "error", err)
	}
	if b != nil {
		// 设置请求头
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("X-MBX-APIKEY", b.ApiKey)
	}
	// 发送请求
	return c.HttpDo(req, logger)
}

// 发送请求并返回响应
func (c *OkAPIClient) SendBroker(data map[string]any, rURL api.APIMethod, b *broker.Broker) (response []byte, err error) {
	// 解析基础 URL
	fullURL := utils.URLJoin(c.url, rURL.Path)
	// 获取当前的时间戳（UTC格式）
	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05.999Z")
	// 如果是POST请求，转换请求数据为JSON
	var body string
	queryString := ""
	if rURL.Method == "POST" {
		bodyBytes, err := json.Marshal(data)
		if err != nil {
			c.logger.Error("Error marshaling request body", "error", err)
		}
		body = string(bodyBytes)
	} else {
		body = "" // 对于GET请求，body为空
		queryString += "?"
		for k, v := range data {
			queryString += fmt.Sprintf("%s=%s&", k, v)
		}
		queryString = queryString[:len(queryString)-1] // 去掉最后一个 '&'
		fullURL += queryString
	}

	// 拼接签名字符串
	signString := fmt.Sprintf("%s%s%s%s", timestamp, rURL.Method, rURL.Path, body+queryString)

	// 生成签名
	sign := c.generateOkSign(signString, b.Secret)
	logger := c.logger.With("url", fullURL).With("data", data).With("sign", sign).With("timestamp", timestamp)
	// 构造请求
	req, err := http.NewRequest(rURL.Method, fullURL, bytes.NewBuffer([]byte(body)))
	if err != nil {
		c.logger.Error("Error creating request: %v", "error", err)
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("OK-ACCESS-KEY", b.ApiKey)
	req.Header.Set("OK-ACCESS-PASSPHRASE", b.PassPhrase)
	req.Header.Set("OK-ACCESS-SIGN", sign)
	req.Header.Set("OK-ACCESS-TIMESTAMP", timestamp)

	// 发送请求
	return c.HttpDo(req, logger)
}

// OKX签名生成函数
func (c *OkAPIClient) generateOkSign(signString, secret string) string {
	// 使用HMAC SHA256加密
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signString))
	// Base64编码输出
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// 提交期货订单
func (c *OkAPIClient) SubmitFuturesOrder(b *broker.Broker, o *order.FuturesOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	// 控制OrdId长度
	o = fixFuturesOrderClOrdId(o)
	// 计算订单价值
	orderValue := math.Abs(o.Size * o.Price)
	// 获取杠杆
	lever, valOk := b.Leverages[o.Symbol]
	if !valOk || lever == 0 {
		return o, fmt.Errorf("leverage not found for symbol %s", o.Symbol)
	}
	// 限价单适用规则
	if o.OrdType == order.LIMIT {
		if o.IsOpen() && orderValue < info.MinNotional {
			// 订单价值小于最小价值
			return o, fmt.Errorf("order value %f is less than min notional %f", orderValue, info.MinNotional)
		}
		if o.IsOpen() && orderValue/float64(lever) > b.Account.Available { // todo float64(b.Leverages[o.Symbol])为0
			// 订单价值大于可用余额
			return o, fmt.Errorf("order value %f is more than available balance %f", orderValue, b.Account.Available)
		}
		// 调整限价
		if o.Price > info.MaxPrice {
			c.logger.Warn("Adjust order price", "price", o.Price, "maxPrice", info.MaxPrice)
			o.Price = info.MaxPrice
		}
		if o.Price < info.MinPrice {
			c.logger.Warn("Adjust order price", "price", o.Price, "minPrice", info.MinPrice)
			o.Price = info.MinPrice
		}
	}
	// 张币系数
	ctVal := info.CtVal
	if o.Size/ctVal < info.MinSize {
		return o, fmt.Errorf("order size %f is less than min size %f", o.Size, info.MinSize)
	}
	orderRequest := &OrderRequest{
		TdMode:  "cross",
		TgtCcy:  "quote_ccy", //按数量下单
		InstId:  okUtils.SymbolToInstIdFutures(o.Symbol),
		ClOrdId: o.ClOrdId,
		Side:    okUtils.MarshalSide(o.Side),
		PosSide: okUtils.MarshalPosSide(o.PosSide),
		OrdType: okUtils.MarshalOrdTypeTimeInForce(o.OrdType, o.TimeInForce),
		Price:   strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		Size:    strconv.FormatFloat(math.Abs(o.Size/ctVal), 'f', info.QuantityPrecision, 64), // todo 转 合约张算精度
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TgtCcy = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		c.logger.Error("Error converting struct to map", "error", err)
		o.ToReject()
		return o, err
	}
	// 将order加入borkerpending中
	b.AddFuturesOrder(o)
	// 添加tag
	params = addTagToOrderParams(params)
	// 签名并返回map
	response, err := c.SendBroker(params, okapi.APIFuturesOrder, b)
	c.logger.Info("Futures order response", "response", string(response)) // todo
	if err != nil {
		// 检查响应, 将order从borkerpending中移除
		o.ToReject()
		b.RemoveFuturesOrder(o)
		c.logger.Error("期货订单提交错误", "error", err, "params", params, "info", info)
		return o, err
	}
	orderResponse := TradeResponse{}
	err = json.Unmarshal(response, &orderResponse)
	if err != nil {
		c.logger.Error("FuturesOrder Submit Error unmarshalling JSON", "error", err)
		o.ToReject()
		b.RemoveFuturesOrder(o)
		return o, err
	}
	if orderResponse.Code != "0" {
		c.logger.Error("FuturesOrder Submit Error", "error", orderResponse.Data)
		for _, d := range orderResponse.Data {
			// 处理错误码
			if d.Code == "51008" {
				// 保证金不足
				c.logger.Warn(d.Msg, "order", o)
				o.ToReject()
				b.RemoveFuturesOrder(o)
				return o, errors.New("margin")
			}
		}
		o.ToReject()
		b.RemoveFuturesOrder(o)
		return o, errors.New(orderResponse.Data[0].Msg)
	}
	o.ToNew()
	return o, nil
}

// 提交期货订单
func (c *OkAPIClient) SubmitMarginOrder(b *broker.Broker, o *order.MarginOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	o = fixMarginOrderClOrdId(o)
	orderValue := math.Abs(o.Size * o.Price)
	if o.Size < info.MinSize {
		return o, fmt.Errorf("order size %f is less than min size %f", o.Size, info.MinSize)
	}
	// 限价单适用规则
	if o.OrdType == order.LIMIT {

		if orderValue < info.MinNotional {
			return o, fmt.Errorf("order value %f is less than min notional %f", orderValue, info.MinNotional)
		}
		if o.IsOpen() && orderValue > b.AssetWallets["USDT"].Free {
			return o, fmt.Errorf("order value %f is more than available balance %f", orderValue, b.AssetWallets["USDT"].Free)
		}
		// 调整价格
		if o.Price > info.MaxPrice {
			c.logger.Warn("Adjust order price", "price", o.Price, "maxPrice", info.MaxPrice)
			o.Price = info.MaxPrice
		}
		if o.Price < info.MinPrice {
			c.logger.Warn("Adjust order price", "price", o.Price, "minPrice", info.MinPrice)
			o.Price = info.MinPrice
		}
	}
	// 将order加入borkerpending中
	b.AddMarginOrder(o)
	orderRequest := &OrderRequest{
		TdMode:  "cash",
		TgtCcy:  "quote_ccy",
		InstId:  okUtils.SymbolToInstIdSpot(o.Symbol),
		ClOrdId: o.ClOrdId,
		Side:    okUtils.MarshalSide(o.Side),
		OrdType: okUtils.MarshalOrdTypeTimeInForce(o.OrdType, o.TimeInForce),
		Price:   strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		Size:    strconv.FormatFloat(o.Size, 'f', info.QuantityPrecision, 64),
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TgtCcy = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		return o, err
	}
	// 添加tag
	params = addTagToOrderParams(params)

	response, err := c.SendBroker(params, okapi.APIMarginOrder, b)
	c.logger.Info("Spot order response", "response", string(response)) // todo
	if err != nil {
		// 检查响应
		// 将order从borkerpending中移除
		b.RemoveMarginOrder(o)
		msg := fmt.Sprintf("现货订单提交错误: %v , params: %+v, info: %+v \n", err, params, info)
		c.logger.Error(msg, "error", err)
		return o, err
	}
	orderResponse := TradeResponse{}
	err = json.Unmarshal(response, &orderResponse)
	if err != nil {
		return o, err
	}
	if orderResponse.Code != "0" {
		c.logger.Error("Submit SpotOrder Error", "error", orderResponse.Data)
		for _, d := range orderResponse.Data {
			if d.Code == "1" && d.Msg == "Order cancellation failed as the order has been filled, canceled or does not exist." {
				// 订单以及被撤销
				c.logger.Warn("现货订单不存在, 手动删除", "order", o)
				b.RemoveMarginOrder(o)
				return o, nil
			}
		}
		return o, errors.New(orderResponse.Data[0].Msg)
	}
	return o, nil
}

// 修改期货订单
func (c *OkAPIClient) ModifyFuturesOrder(b *broker.Broker, o order.FuturesOrder, info *data.ExchangeInfo) error {
	// 检查order是否在挂单中
	if !b.HasFuturesOrder(o.ClOrdId) {
		c.logger.Warn("Order not found in pending orders", "order", o)
		return nil
	}
	orderValue := math.Abs(o.Size * o.Price)
	if orderValue < info.MinNotional {
		return fmt.Errorf("order value %f is less than min notional %f", orderValue, info.MinNotional)
	}
	if o.IsOpen() && orderValue > b.AssetWallets["USDT"].Free {
		return fmt.Errorf("order value %f is more than available balance %f", orderValue, b.AssetWallets["USDT"].Free)
	}

	// 调整价格
	o.Price = math.Min(o.Price, info.MaxPrice)
	o.Price = math.Max(o.Price, info.MinPrice)

	ctVal := info.CtVal

	if o.Size/ctVal < info.MinSize {
		return fmt.Errorf("order size %f is less than min size %f", o.Size, info.MinSize)
	}

	params := map[string]any{
		"instId":  okUtils.SymbolToInstIdFutures(o.Symbol),
		"clOrdId": o.ClOrdId,
		"newPx":   strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		"newSz":   strconv.FormatFloat(math.Abs(o.Size/ctVal), 'f', info.QuantityPrecision, 64),
	}

	response, err := c.SendBroker(params, okapi.APIModifyFuturesOrder, b)
	c.logger.Info("Modify Futures order response", "response", string(response)) // todo
	if err != nil {
		// 检查响应
		c.logger.Error("Modify Futures order Error", "error", err)
		return err
	}
	orderResponse := TradeResponse{}
	err = json.Unmarshal(response, &orderResponse)
	if err != nil {
		return err
	}
	if orderResponse.Code != "0" {
		return errors.New(orderResponse.Data[0].Msg)
	}
	return nil
}

// 更新账户余额
func (c *OkAPIClient) updateBrokerBalance(b *broker.Broker) error {
	response, err := c.SendBroker(map[string]any{}, okapi.APIGetBalance, b)
	if err != nil {
		return err
	}

	// 解析响应
	var balanceResponse BalanceResponse
	err = json.Unmarshal(response, &balanceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	balanceResponse.UpdateBroker(b)

	return nil
}

// 请求账户余额
func (c *OkAPIClient) QueryAccount(b *broker.Broker) (string, error) {
	response, err := c.SendBroker(map[string]any{}, okapi.APIGetBalance, b)
	if err != nil {
		return "", err
	}
	return string(response), nil
}

// 检查账户模式是否正确
func (c *OkAPIClient) updateBrokerAccountType(b *broker.Broker) error {
	response, err := c.SendBroker(map[string]any{}, okapi.APIGetAccountConfig, b)
	if err != nil {
		return err
	}
	// 解析响应
	var accountConfigResponse AccountConfigResponse
	err = json.Unmarshal(response, &accountConfigResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	// 只支持 现货和合约模式
	if accountConfigResponse.Data[0].AccountLevel != "2" {
		return fmt.Errorf("account type %s is not supported", accountConfigResponse.Data[0].AccountLevel)
	}
	return nil
}

func (c *OkAPIClient) updateBrokerCtValues(b *broker.Broker) error {
	response, err := c.SendFutures(map[string]any{
		"instType": "SWAP",
	}, okapi.APIGetExchangeInfo, nil)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	exchangeInfoResponse.UpdateBroker(b)

	return nil
}

// 查询账户持仓
func (c *OkAPIClient) updateBrokerPosition(b *broker.Broker) error {
	response, err := c.SendBroker(map[string]any{}, okapi.APIGetPositions, b)
	if err != nil {
		return err
	}

	// 解析响应
	var positionResponse PositionsResponse
	err = json.Unmarshal(response, &positionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	positionResponse.UpdateBroker(b)

	return nil
}

// 查询UM账户未完成订单
func (c *OkAPIClient) queryUMPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.SendBroker(map[string]any{"instId": okUtils.SymbolToInstIdFutures(s)}, okapi.APIGetPending, b)

	if err != nil {
		c.logger.Error("QueryUMPending response Error :", "error", err)
		return err
	}

	// 解析响应
	var brokerUMPendingResponse FuturesPendingResponse
	err = json.Unmarshal(response, &brokerUMPendingResponse)
	if err != nil {
		c.logger.Error("QueryUMPending BrokerPendingResponse Error unmarshalling JSON:", "error", err)
		return err
	}
	brokerUMPendingResponse.UpdateBroker(b)

	return nil
}

// 查询杠杆账户未完成订单
func (c *OkAPIClient) queryMarginPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.SendBroker(map[string]any{"instId": okUtils.SymbolToInstIdSpot(s)}, okapi.APIGetPending, b)

	if err != nil {
		c.logger.Error("QueryMarginPending response Error :", "error", err)
		return err
	}

	// 解析响应
	var brokerMarginPendingResponse MarginPendingResponse
	err = json.Unmarshal(response, &brokerMarginPendingResponse)
	if err != nil {
		c.logger.Error("QueryMarginPending MarginBrokerPendingResponse Error unmarshalling JSON:", "error", err)
		return err
	}

	brokerMarginPendingResponse.UpdateBroker(b)

	return nil
}

// // 查看合约账户杠杆
// func (c *OkAPIClient) queryUMLeverage(b *broker.Broker) error {
// 	response, err := c.SendBroker(map[string]any{
// 		"mgnMode": "cross",
// 		"ccy":     "USDT",
// 	}, okapi.APIGetUMLeverageInfo, b)

// 	if err != nil {
// 		return err
// 	}

// 	// 解析响应
// 	var brokerUMConfigResponse BrokerUMConfigResponse
// 	err = json.Unmarshal(response, &brokerUMConfigResponse)
// 	if err != nil {
// 		c.logger.Error("Error unmarshalling JSON:", "error", err)
// 		return err
// 	}
// 	brokerUMConfigResponse.UpdateBroker(b)

// 	return nil
// }

func (c *OkAPIClient) UpdateFuturesLeverageSingle(b *broker.Broker, d *data.SymbolData) (err error) {
	response, err := c.SendBroker(map[string]any{
		"mgnMode": "cross",
		"instId":  okUtils.SymbolToInstIdFutures(d.Symbol),
	}, okapi.APIGetUMLeverageInfo, b)

	if err != nil {
		c.logger.Error("UpdateFuturesLeverageSingle response Error :", "error", err)
		return err
	}

	// 解析响应
	var brokerUMConfigResponse BrokerUMConfigResponse
	err = json.Unmarshal(response, &brokerUMConfigResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	brokerUMConfigResponse.UpdateBroker(b)
	return nil
}

// 查询期货交易规范
func (c *OkAPIClient) updateFuturesDataInfo(ds []*data.SymbolData) error {
	response, err := c.SendFutures(map[string]any{
		"instType": "SWAP",
		// "instId":   okUtils.SymbolToInstIdFutures(ds[0].Symbol),
	}, okapi.APIGetExchangeInfo, nil)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	exchangeInfoResponse.UpdateData(ds)

	return nil
}

// 查询期货资金费率
func (c *OkAPIClient) updateFuturesFunding(ds []*data.SymbolData) error {
	for _, d := range ds {
		response, err := c.SendFutures(map[string]any{
			"instId": okUtils.SymbolToInstIdFutures(d.Symbol),
		}, okapi.APIGetFuturesFunding, nil)
		if err != nil {
			return err
		}
		// 解析响应
		var fundingResponse FuturesFundingResponse
		err = json.Unmarshal(response, &fundingResponse)
		if err != nil {
			c.logger.Error("Error unmarshalling JSON:", "error", err)
			return err
		}
		fundingResponse.UpdateData([]*data.SymbolData{d})
	}
	return nil
}

// // 查询期货历史资金费率
// func (c *OkAPIClient) updateFuturesFundingHist(d *data.SymbolData) error {
// 	response, err := c.SendFutures(map[string]any{
// 		"instId": okUtils.SymbolToInstIdFutures(d.Symbol),
// 		"limit":  1,
// 	}, okapi.APIGetFuturesFundingHist, nil)
// 	if err != nil {
// 		return err
// 	}
// 	// 解析响应
// 	var fundingHistResponse FuturesFundingHistResponse
// 	err = json.Unmarshal(response, &fundingHistResponse)
// 	if err != nil {
// 		c.logger.Error("Error unmarshalling JSON:", "error", err)
// 		return err
// 	}
// 	fundingHistResponse.UpdateData(d)
// 	return nil
// }

// 查询现货交易规范
func (c *OkAPIClient) updateSpotDataInfo(ds []*data.SymbolData) error {
	response, err := c.SendSpot(map[string]any{
		"instType": "SPOT",
		// "instId":   okUtils.SymbolToInstIdSpot(ds[0].Symbol),
	}, okapi.APIGetExchangeInfo, nil)
	if err != nil {
		c.logger.Error("updateSpotDataInfo", "error", err)
		return err
	}
	// 解析响应
	var exchangeInfoResponse SpotExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	exchangeInfoResponse.UpdateData(ds)
	return nil
}

func (c *OkAPIClient) updateDepthMaxSize(ds []*data.SymbolData) {
	for _, d := range ds {
		d.OrderBooks.MaxSize = 400
	}
}

// 取消所有期货订单
func (c *OkAPIClient) CancelAllFutureOrders(b *broker.Broker, d *data.SymbolData) (err error) {
	// 取消所有期货订单
	for _, o := range b.GetFuturesOrders(d.Symbol) {
		err := c.CancelFuturesOrder(b, o)
		if err != nil {
			c.logger.Error("Cancel All Futures Order Error", "error", err)
		}
	}
	c.logger.Info("全部期货订单已取消", "symbol", d.Symbol)
	return nil
}

// 取消所有杠杆订单
func (c *OkAPIClient) CancelAllMarginOrders(b *broker.Broker, d *data.SymbolData) error {
	for _, o := range b.GetMarginOrders(d.Symbol) {
		err := c.CancelMarginOrder(b, o)
		if err != nil {
			c.logger.Error("Cancel All Margin Order Error", "error", err)
		}
	}
	c.logger.Info("全部杠杆订单已取消", "symbol", d.Symbol)
	return nil
}

// 取消期货订单
func (c *OkAPIClient) CancelFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error {
	params := map[string]any{"instId": okUtils.SymbolToInstIdFutures(o.Symbol)}
	if o.ClOrdId != "" {
		params["clOrdId"] = o.ClOrdId
	} else {
		params["ordId"] = o.OrdId
	}
	res, err := c.SendBroker(params, okapi.APICancelOrder, b)
	if err != nil {
		c.logger.Error("CancelFuturesOrder Error", "error", err, "res", res, "clOrdId", o.ClOrdId)
		return err
	}
	response := &TradeResponse{}
	err = json.Unmarshal(res, response)
	if err != nil {
		c.logger.Error("CancelFuturesOrder Error unmarshalling JSON:", "error", err)
		return err
	}
	if response.Code != "0" {
		c.logger.Error("CancelFuturesOrder Code Error", "error", response.Data)
		for _, d := range response.Data {
			if d.Code == "51400" {
				// 订单以及被撤销
				c.logger.Warn("期货订单不存在, 手动删除", "order", o)
				// 查询订单
				c.QueryFuturesOrder(b, o)
				b.RemoveFuturesOrder(o)
				continue
			}
			if d.Code == "51006" {
				// 订单限价撤销
				c.logger.Warn("委托价格不在限价范围内, 手动删除", "order", o)
				b.RemoveFuturesOrder(o)
				continue
			}

		}
		return errors.New(response.Data[0].Msg)
	}
	c.logger.Info("期货订单已取消", "order", o)
	return nil
}

// 取消杠杆订单
func (c *OkAPIClient) CancelMarginOrder(b *broker.Broker, o *order.MarginOrder) error {
	params := map[string]any{"instId": okUtils.SymbolToInstIdSpot(o.Symbol)}
	if o.ClOrdId != "" {
		params["clOrdId"] = o.ClOrdId
	} else {
		params["ordId"] = o.OrdId
	}
	res, err := c.SendBroker(params, okapi.APICancelOrder, b)
	if err != nil {
		c.logger.Error("CancelFuturesOrder Error", "error", err, "res", res, "clOrdId", o.ClOrdId)
		return err
	}
	response := &TradeResponse{}
	err = json.Unmarshal(res, response)
	if err != nil {
		c.logger.Error("CancelFuturesOrder Error unmarshalling JSON:", "error", err)
		return err
	}
	if response.Code != "0" {
		c.logger.Error("CancelFuturesOrder Code Error", "error", response.Data)
		for _, d := range response.Data {
			if d.Code == "51400" {
				// 订单以及被撤销
				c.logger.Warn("现货订单不存在, 手动删除", "order", o)
				b.RemoveMarginOrder(o)
				continue
			}
			if d.Code == "51006" {
				// 订单限价撤销
				c.logger.Warn("委托价格不在限价范围内, 手动删除", "order", o)
				b.RemoveMarginOrder(o)
				continue
			}

		}
		return errors.New(response.Data[0].Msg)
	}
	c.logger.Info("杠杆订单已取消", "order", o)
	return nil
}

// 查询期货所有可交易品种
func (c *OkAPIClient) QueryAllTradingFutures() (symbols []string) {
	params := map[string]any{"instType": "SWAP"}
	msg, err := c.SendFutures(params, okapi.APIGetExchangeInfo, nil)
	if err != nil {
		c.logger.Error("QueryAllTradingMargin Error", "error", err)
		return []string{}
	}
	// 解析响应
	var exchangeInfoResp SpotExchangeInfoResponse
	err = json.Unmarshal(msg, &exchangeInfoResp)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON for QueryAllTradingFutures", "error", err)
		return []string{}
	}
	// 提取可交易品种
	for _, data := range exchangeInfoResp.Data {
		if data.State == "live" && data.SettleCcy == "USDT" {
			symbol := okUtils.InstIdFuturesToSymbol(data.InstId)
			symbols = append(symbols, symbol)
		}
	}
	return symbols
}

// 查询期货所有资金费率
func (c *OkAPIClient) QueryAllFunding() (r map[string]float64) {
	c.logger.Warn("不支持查询全币种资金费率")
	return r
}

// 查询所有可交易杠杆品种
func (c *OkAPIClient) QueryAllTradingMargin(b *broker.Broker) (symbols []string) {
	params := map[string]any{"instType": "SPOT"}
	msg, err := c.SendSpot(params, okapi.APIGetExchangeInfo, nil)
	if err != nil {
		c.logger.Error("QueryAllTradingMargin Error", "error", err)
		return []string{}
	}
	// 解析响应
	var exchangeInfoResp SpotExchangeInfoResponse
	err = json.Unmarshal(msg, &exchangeInfoResp)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON for QueryAllTradingMargin", "error", err)
		return []string{}
	}
	// 提取可交易品种
	for _, data := range exchangeInfoResp.Data {
		if data.State == "live" && data.QuoteCcy == "USDT" {
			symbol := okUtils.InstIdSpotToSymbol(data.InstId)
			symbols = append(symbols, symbol)
		}
	}
	return symbols
}

// 期货平仓
func (c *OkAPIClient) CloseFutures(b *broker.Broker, d *data.SymbolData) error {
	// 期货多仓平仓
	if math.Abs(b.GetFuturesSizeLong(d.Symbol)) > 0 {
		futuresOrderLong := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeLong(d.Symbol))).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.MARKET).SetTimeInForce(order.GTC)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderLong, d.Info,
		)
		if err != nil {
			c.logger.Error("Close FuturesOrder Long Error", "error", err)
		}
		return err
	}
	if math.Abs(b.GetFuturesSizeShort(d.Symbol)) > 0 {
		// 期货空仓平仓
		futuresOrderShort := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeShort(d.Symbol))).SetSide(order.BUY).SetPosSide(order.SHORT).SetOrdType(order.MARKET).SetTimeInForce(order.GTC)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderShort, d.Info,
		)
		if err != nil {
			c.logger.Error("Close FuturesOrder Short Error", "error", err)
		}
		return err
	}
	return nil
}

// 现货平仓
func (c *OkAPIClient) CloseMargin(b *broker.Broker, d *data.SymbolData) error {
	size := b.GetAssetTotalFloor(d.Asset, d.Info)
	if size > 0 {
		marginOrder := order.CreateMarginOrder().SetSymbol(d.Symbol).SetPrice(d.Price.Last()).
			SetSize(size).SetSide(order.SELL).SetOrdType(order.MARKET)
		_, err := c.SubmitMarginOrder(
			b, marginOrder, d.Info,
		)
		if err != nil {
			c.logger.Error("Close MarginOrder error", "error", err)
			return err
		}
	}
	return nil
}

// 修改期货杠杆
func (c *OkAPIClient) ModifyFuturesLeverage(b *broker.Broker, d *data.SymbolData, lever int64) error {
	_, err := c.SendBroker(map[string]any{"instId": okUtils.SymbolToInstIdFutures(d.Symbol), "lever": lever, "mgnMode": "cross"}, okapi.APIModifyFuturesLeverage, b)
	if err != nil {
		c.logger.Error("ModifyFuturesLeverage error", "error", err)
		return err
	}
	c.logger.Info("修改杠杆成功", "symbol", d.Symbol, "leverage", lever)
	// TODO 可能会有并发问题
	b.Leverages[d.Symbol] = lever
	return nil
}

func (c *OkAPIClient) SubmitOrder(b *broker.Broker, o *order.Order, d *data.SymbolData) (client.OrderViewer, error) {
	switch d.DataType {
	case data.Futures:
		var posSide order.PosSide
		// 根据当前仓位和买卖方向判断posSide
		sizeLong, sizeShort := b.GetFuturesSize(d.Symbol)
		size := sizeLong + sizeShort
		switch {
		case size > 0:
			posSide = order.LONG
		case size < 0:
			posSide = order.SHORT
		case o.Side == order.SELL:
			posSide = order.SHORT
		case o.Side == order.BUY:
			posSide = order.LONG
		}
		fo := order.CreateFuturesOrderFromCommon(o, posSide)
		return c.SubmitFuturesOrder(b, fo, d.Info)
	case data.Spot:
		mo := order.CreateMarginOrderFromCommon(o)
		return c.SubmitMarginOrder(b, mo, d.Info)
	}
	return nil, nil
}

func (c *OkAPIClient) CancelAllOrders(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CancelAllFutureOrders(b, d)
	case data.Spot:
		return c.CancelAllMarginOrders(b, d)
	}
	return nil
}

func (c *OkAPIClient) Close(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CloseFutures(b, d)
	case data.Spot:
		return c.CloseMargin(b, d)
	}
	return nil
}

func (c *OkAPIClient) UpdateDatas(ds []*data.SymbolData) error {
	fd := make([]*data.SymbolData, 0)
	sd := make([]*data.SymbolData, 0)

	for _, d := range ds {
		switch d.DataType {
		case data.Futures:
			fd = append(fd, d)
		case data.Spot:
			sd = append(sd, d)
		}
	}

	var err error
	err = c.UpdateFuturesDatas(fd)
	if err != nil {
		return err
	}
	err = c.UpdateSpotDatas(sd)
	if err != nil {
		return err
	}
	return nil
}

// 查询仓位档位
func (c *OkAPIClient) UpdatePositionTiers(b *broker.Broker, d *data.SymbolData) error {
	// if d.DataType != data.Futures {
	// 	return nil
	// }
	params := map[string]any{"instType": "SWAP", "tdMode": "cross", "instFamily": okUtils.SymbolToInstFamily(d.Symbol)}
	c.tiersRequestLimiter.Acquire(context.Background()) // todo 限流器
	msg, err := c.SendFutures(params, okapi.APIGetPositionTiers, nil)
	if err != nil {
		c.logger.Error("UpdatePositionTiers Error", "error", err)
		return err
	}

	// 解析响应
	var positionTiersResponse PositionTiersResponse
	err = json.Unmarshal(msg, &positionTiersResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	positionTiersResponse.UpdateBroker(b)
	return nil
}

// 查询张币系数
func (c *OkAPIClient) UpdateCtVal(b *broker.Broker, d *data.SymbolData) error {
	return c.updateBrokerCtValues(b)
}

// 查询期货订单
func (c *OkAPIClient) QueryFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error {
	_, err := c.SendBroker(map[string]any{"instId": okUtils.SymbolToInstIdFutures(o.Symbol), "clOrdId": o.ClOrdId}, okapi.APIQueryFuturesOrder, b)
	if err != nil {
		return err
	}
	// 解析响应
	var orderResponse FuturesPendingResponse
	err = json.Unmarshal([]byte{}, &orderResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	orderResponse.UpdateBroker(b)
	return nil
}

// 查询期货手续费
func (c *OkAPIClient) updateFuturesDataComm(b *broker.Broker) error {
	response, err := c.SendBroker(map[string]any{"instType": "SWAP", "instFamily": "BTC-USDT"}, okapi.APIGetCommissionRate, b)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse CommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

// 查询现货手续费
func (c *OkAPIClient) updateSpotDataComm(b *broker.Broker) error {
	response, err := c.SendBroker(map[string]any{"instType": "SPOT", "instId": "BTC-USDT"}, okapi.APIGetCommissionRate, b)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse CommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

// 更新深度(全量)
func (c *OkAPIClient) UpdateDepth(d *data.SymbolData) error {
	return nil
}

func (c *OkAPIClient) Query24hrQuote(d *data.SymbolData) (float64, error) {
	params := map[string]any{"bar": "1H", "limit": "24"}
	if d.DataType == data.Futures {
		params["instId"] = okUtils.SymbolToInstIdFutures(d.Symbol)
	} else if d.DataType == data.Spot {
		params["instId"] = okUtils.SymbolToInstIdSpot(d.Symbol)
	}
	response, err := c.sendPublic(params, okapi.APIGetKlines, nil)
	if err != nil {
		return 0, err
	}
	// 解析响应
	var klines KLineResponse
	err = json.Unmarshal(response, &klines)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	quote := 0.0
	for _, d := range klines.Data {
		quote += utils.StringToFloat64(d[7])
	}
	return quote, nil
}

// 查询持仓量
func (c *OkAPIClient) QueryOpenInterest(d *data.SymbolData) (float64, error) {
	params := map[string]any{"instId": okUtils.SymbolToInstIdFutures(d.Symbol)}
	response, err := c.sendPublic(params, okapi.APIGetFuturesOpenInterest, nil)

	if err != nil {
		return 0, err
	}
	// 解析响应
	var openInterestResponse OpenInterestResponse
	err = json.Unmarshal(response, &openInterestResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	count := utils.StringToFloat64(openInterestResponse.Data[0].OICcy)
	return count, nil
}
