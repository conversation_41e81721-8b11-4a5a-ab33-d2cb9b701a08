package config

const (
	// 任务redis
	TaskAddr     string = "43.155.69.64:6379"
	TaskPassword string = "idg68886"
	TaskDB       int    = 0
	TESTDB       int    = 1
	FundingDB    int    = 2
	MetaDB       int    = 5

	// 东京redis
	TokyoRedisAddr     string = "43.163.231.45:6379"
	TokyoRedisPassword string = "fhj666888"
	TokyoDiffDB        int    = 0
	TokyoPositionDB    int    = 13

	// 香港redis
	HKRedisAddr     string = "47.239.69.245:6379"
	HKRedisPassword string = "idg68886"
	HKDiffDB        int    = 0

	InfoTable      string = "arb_info"
	InfoTableTokyo string = "arb_info_tokyo"
	InfoTableCmp   string = "arb_info_cmp"

	ArbitTaskTable       string = "arb_tasks"
	FundTaskTable        string = "fund_tasks"
	BnCrossFundTaskTable string = "bn_cross_fund_tasks"
	BnCrossDiffTaskTable string = "bn_cross_diff_tasks"

	LargeOrderTaskTable string = "large_order_tasks"
	GridTaskTable       string = "grid_tasks"

	// 跨所价差表
	CrossDiffTable string = "cross_arb_diff"
	// 欧意价差表
	OkDiffTable string = "ok_arb_diff"
	// 币安价差表
	BnDiffTable string = "bn_arb_diff"

	// 币安统一账户现货
	BnBalance string = "binance_balance_unified"
	// 币安现货价格
	BnAssetPrice string = "binance_symbol_price_spot"
	// 币安传统账户合约
	BnPosition string = "binance_positions"
	// 币安统一账户合约
	BnUnifiedPosition string = "binance_unified_positions"
	// 欧易现货
	OkBalance string = "ok_balance"
	// 欧易合约
	OkPosition string = "ok_positions"
	// 欧易合约价格
	OkAssetPrice string = "ok_symbol_price_spot"
	// 币安资金费信息
	BnFundingInfo string = "bn_funding_info"
	// 欧易资金费信息
	OkFundingInfo string = "ok_funding_info"

	// 币安期货品种元信息表
	BnFuturesMetaInfo string = "bn_futures_symbolinfo"
	// 欧易期货品种元信息表
	OkFuturesMetaInfo string = "ok_futures_symbolinfo"
	// 币安现货品种元信息表
	BnSpotMetaInfo string = "bn_spot_symbolinfo"
	// 欧易现货品种元信息表
	OkSpotMetaInfo string = "ok_spot_symbolinfo"
)
