package arbit

import (
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"context"
	"encoding/json"
	"log/slog"
	"math"
	"strconv"
	"time"
)

// redis状态信息
type CrossRedisStatus struct {
	Diff               string `json:"diff_rate"`
	IndexDiff          string `json:"index_diff_rate"`
	FundingCost        string `json:"funding_cost"`
	LeftFundingPeriod  string `json:"left_funding_period"`
	RightFundingPeriod string `json:"right_funding_period"`
	LeftTime           string `json:"left_time"`
	RightTime          string `json:"right_time"`
}

type DiffRedis struct {
	/*
		价差记录redis
		记录两腿价差, 以及左腿期指价差
	*/
	// 左腿
	LeftData *data.SymbolData
	// 右腿
	RightData *data.SymbolData
	// 展示信息
	redisStatusChan chan []byte
	// redis上下文
	redisCtx *strategy.RedisCtx
	// pip hset chan
	hsetChan chan map[string]string
	// 价差数据ema
	diffEMA *utils.EMA
	// 日志
	logger *slog.Logger
	// 停止channel
	stopChan chan struct{}
}

func NewDiffRedis(
	ld *data.SymbolData, rd *data.SymbolData,
	redisCtx *strategy.RedisCtx,
	hsetChan chan map[string]string,
	logger *slog.Logger,
) *DiffRedis {
	return &DiffRedis{
		LeftData:        ld,
		RightData:       rd,
		redisStatusChan: make(chan []byte),
		redisCtx:        redisCtx,
		hsetChan:        hsetChan,
		diffEMA:         utils.NewEMA(10*10, true),
		logger:          logger,
		stopChan:        make(chan struct{}),
	}
}

func (stra *DiffRedis) Start() {
	// 检测数据源
	go func() {
		stra.logger.Debug("开始运行")
		for {
			select {
			case <-stra.stopChan:
				stra.logger.Debug("停止运行")
				return
			case <-stra.LeftData.TriggerChan:
				stra.next("left")
			case <-stra.RightData.TriggerChan:
				stra.next("right")
			}
		}
	}()

	// 写入redis
	if stra.redisCtx != nil {
		go func() {
			ticker := time.NewTicker(time.Second)
			defer ticker.Stop()
			for {
				select {
				case <-stra.stopChan:
					stra.logger.Info("Received signal to stop update redis")
					return
				case <-ticker.C:
					stra.writeRedis()
				}
			}
		}()
	}
}

func (stra *DiffRedis) Stop() {
	select {
	case <-stra.stopChan:
		stra.logger.Debug("stop-trigger has already been sent")
	default:
		close(stra.stopChan)
		stra.logger.Debug("stop-trigger has been sent")
	}
	// 从redis中删除key
	if stra.redisCtx != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		stra.redisCtx.Client.HDel(ctx, stra.redisCtx.Table, stra.LeftData.Symbol)
		cancel()
	}
}

func (stra *DiffRedis) next(leg string) {
	// TODO
	leftPrice := stra.LeftData.Bid1Price()
	indexPrice := stra.LeftData.Index.Last()
	leftTs := int64(stra.LeftData.Time.Last())
	rightPrice := stra.RightData.Ask1Price()
	rightTs := int64(stra.RightData.Time.Last())
	if leg == "left" {
		// 整数转换为字符串
		stra.logger.Info("left leg update", "lprice", leftPrice, "rprice", rightPrice, "lts", leftTs, "rts", rightTs)
	} else {
		stra.logger.Info("right leg update", "lprice", leftPrice, "rprice", rightPrice, "lts", leftTs, "rts", rightTs)
	}
	if leftPrice == 0 || rightPrice == 0 {
		// 价格为0, 跳过
		stra.logger.Debug("price is 0, skip")
		return
	}

	// 价差, 绝对值
	diff := (leftPrice - rightPrice) / rightPrice * 100
	// EMA价差
	emaDiff := stra.calcDiff(diff)

	// 期指价差
	indexDiff := (leftPrice - indexPrice) / indexPrice * 100

	// 资金费成本
	leftFundingCost, rightFundingCost := CalcFundingCost(stra.LeftData, stra.RightData)
	fundingCost := leftFundingCost + rightFundingCost

	// 资金费周期
	leftFundingPeriodHours := stra.LeftData.FundingPeriod().Hours()
	rightFundingPeriodHours := stra.RightData.FundingPeriod().Hours()

	// 传递redis信息
	redisStatus := CrossRedisStatus{
		Diff:               strconv.FormatFloat(emaDiff, 'f', -1, 64),
		IndexDiff:          strconv.FormatFloat(indexDiff, 'f', -1, 64),
		FundingCost:        strconv.FormatFloat(fundingCost, 'f', -1, 64),
		LeftFundingPeriod:  strconv.FormatFloat(leftFundingPeriodHours, 'f', 0, 64),
		RightFundingPeriod: strconv.FormatFloat(rightFundingPeriodHours, 'f', 0, 64),
		LeftTime:           strconv.FormatInt(leftTs, 10),
		RightTime:          strconv.FormatInt(rightTs, 10),
	}

	// 转为json
	redisstring, _ := json.Marshal(redisStatus)

	utils.SendNonBlocking(stra.redisStatusChan, redisstring)
}

// 是否正在运行
func (stra *DiffRedis) Running() bool {
	select {
	case <-stra.stopChan:
		return false
	default:
		return true
	}
}

// 写入redis
func (stra *DiffRedis) writeRedis() {
	// 从chan中获取数据
	jsonstring := <-stra.redisStatusChan
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	select {
	case stra.hsetChan <- map[string]string{
		stra.LeftData.Symbol: string(jsonstring),
	}:
		stra.logger.Info("write redis success")
	case <-ctx.Done():
		stra.logger.Warn("send redis hset channel timeout")
		return
	}
}

// 更新价差
func (stra *DiffRedis) calcDiff(diff float64) float64 {

	newDiff := stra.diffEMA.Update(diff)
	if math.IsNaN(newDiff) {
		return math.NaN()
	}
	return newDiff

}
