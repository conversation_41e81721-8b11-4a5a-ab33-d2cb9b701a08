package arbit

import (
	config "GoTrader/internal"
	"GoTrader/pkg/queue"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"strconv"
	"time"
)

// 跨所资金费做多
type CrossFundArbitrage struct {
	ArbitrageBase
	// 开仓价差
	openDiff float64
	// 平仓价差
	closeDiff float64
	// 右腿开仓价差
	rightOpenDiff float64
	// 右腿平仓价差
	rightCloseDiff float64
	// 开仓仓位
	openSize float64
	// 价差数据
	diffs *queue.QueueFloat64
	// 左腿期指价差数据
	leftIndexDiffs *queue.QueueFloat64
	// 右腿期指价差数据
	rightIndexDiffs *queue.QueueFloat64
	// 状态Ticker
	statusTicker *time.Ticker
	// 资金费Ticker
	fundTicker *time.Ticker
	// 通知Ticker
	notifyTicker *time.Ticker
	// 通知日志
	notifyLogger *slog.Logger
	// 对照组
	cmpLeg *ArbitLeg
	// 基准组
	baseLeg *ArbitLeg
}

func NewCrossFundArbitrage(
	lg *ArbitLeg, rg *ArbitLeg,
	params ArbitrageParams,
	redisCtx *strategy.RedisCtx,
	hsetChan chan map[string]string,
	logger *slog.Logger,
	notifyLogger *slog.Logger,
) *CrossFundArbitrage {
	logger = logger.With(slog.String("strategy", "CrossFundArbitrage")).With(slog.String("symbol", params.Symbol)).With(slog.String("ID", params.ID))
	if notifyLogger == nil {
		notifyLogger = logger
	}
	return &CrossFundArbitrage{ArbitrageBase: ArbitrageBase{
		LeftLeg:         lg,
		RightLeg:        rg,
		Params:          params,
		status:          params.Status,
		stopChan:        make(chan struct{}),
		redisStatusChan: make(chan []byte),
		redisCtx:        redisCtx,
		redisHsetChan:   hsetChan,
		monitorTicker:   time.NewTicker(time.Second * 300),
		monitorChan:     make(chan struct{}, 1),
		logger:          logger,
	},
		openDiff:        params.OpenDiffLow,
		closeDiff:       params.CloseDiff,
		rightOpenDiff:   params.Extra.RightOpenDiff,
		rightCloseDiff:  params.Extra.RightCloseDiff,
		openSize:        0,
		diffs:           queue.NewQueueFloat64(40),
		leftIndexDiffs:  queue.NewQueueFloat64(40),
		rightIndexDiffs: queue.NewQueueFloat64(40),
		statusTicker:    time.NewTicker(time.Second * 60),
		fundTicker:      time.NewTicker(time.Second * 60),
		notifyTicker:    time.NewTicker(time.Second * 60),
		notifyLogger:    notifyLogger,
		cmpLeg:          lg,
		baseLeg:         rg,
	}
}

func (stra *CrossFundArbitrage) Start() {
	// 更新时区
	loc, _ := time.LoadLocation(config.TimeZone)
	stra.location = loc

	// 左OK 右BN
	if stra.Params.LeftExchange != "OK" || stra.Params.RightExchange != "BN" {
		stra.logger.Error("左右腿交易所不匹配, 仅支持左OK右BN")
		return
	}

	// 更新时区
	stra.location, _ = time.LoadLocation(config.TimeZone)

	// 确定对照组
	stra.cmpLeg, stra.baseLeg = BnAsBaseLeg(&stra.Params, stra.LeftLeg, stra.RightLeg)

	// 初始化broker
	InitBroker(stra.Params, stra.LeftLeg, stra.RightLeg, stra.logger)

	if !BrokerValidCheck(stra.LeftLeg, stra.RightLeg, stra.logger) {
		stra.logger.Error("broker有效性检查失败, 停止运行")
		stra.Pause()
		return
	}

	// 数据源检测
	go DataMonitor(stra.LeftLeg, stra.RightLeg, stra.stopChan, stra.monitorChan, stra.next, stra.logger)

	// 心跳检查
	go Heartbeat(stra.LeftLeg, stra.RightLeg, stra.stopChan, stra.monitorChan, stra.monitorTicker, stra.logger)

	// 写入redis
	go strategy.WriteRedis(stra.redisCtx, stra.Params, stra.stopChan, stra.redisStatusChan, stra.redisHsetChan, stra.logger)

	stra.logger.Info("初始化完毕")
}

func (stra *CrossFundArbitrage) next() {

	// 强制平仓状态退出
	if stra.status == strategy.Stop && stra.RightLeg.Broker.GetDataSize(stra.RightLeg.Data) == 0 && stra.LeftLeg.Broker.GetDataSize(stra.LeftLeg.Data) == 0 {
		stra.logger.Info("任务已平掉所有仓位, 关闭任务")
		stra.Pause()
		return
	}

	if stra.rightOpenDiff > stra.rightCloseDiff {
		stra.logger.Warn("进出价差设置不合理", "bnOpenDiff", strconv.FormatFloat(stra.rightOpenDiff, 'f', 6, 64), "bnCloseDiff", strconv.FormatFloat(stra.rightCloseDiff, 'f', 6, 64))
		return
	}

	// 交易逻辑
	leftPrice := stra.LeftLeg.Data.Bid1Price()
	leftTs := int64(stra.LeftLeg.Data.Time.Last())
	rightPrice := stra.RightLeg.Data.Ask1Price()
	rightTs := int64(stra.RightLeg.Data.Time.Last())
	nts := time.Now().UnixMilli()
	ntime := time.UnixMilli(nts).In(stra.location).Format("2006-01-02 15:04:05.000 MST")
	leftTime := time.UnixMilli(leftTs).In(stra.location).Format("2006-01-02 15:04:05.000 MST")
	rightTime := time.UnixMilli(rightTs).In(stra.location).Format("2006-01-02 15:04:05.000 MST")

	if leftTs == 0 {
		stra.logger.Info("左腿数据源未准备完成")
		return
	}
	if rightTs == 0 {
		stra.logger.Info("右腿数据源未准备完成")
		return
	}

	// 计算开仓仓位
	stra.openSize = CalcOpenSize(stra.LeftLeg, stra.RightLeg, stra.Params, stra.openSize, stra.logger)
	if stra.openSize <= 0 {
		return
	}

	// 价差, 百分比
	diff, topDiff, bottomDiff := CalcDiffs(leftPrice, rightPrice, stra.diffs)

	// 左腿期指价差
	leftIndexDiff, leftIndexTopDiff, leftIndexBottomDiff := CalcDiffs(stra.LeftLeg.Data.Bid1Price(), stra.LeftLeg.Data.Index.Last(), stra.leftIndexDiffs)
	// 右腿期指价差
	rightIndexDiff, rightIndexTopDiff, rightIndexBottomDiff := CalcDiffs(stra.RightLeg.Data.Ask1Price(), stra.RightLeg.Data.Index.Last(), stra.rightIndexDiffs)

	// 传递redis信息
	redisStatus := RedisStatus{
		Diff: strconv.FormatFloat(diff, 'f', -1, 64),
		Time: ntime,
	}
	// 转为json
	redisstring, _ := json.Marshal(redisStatus)
	utils.SendNonBlocking(stra.redisStatusChan, redisstring)

	// 杠杆倍数
	leftLeverage := stra.LeftLeg.Broker.DataLeverage(stra.LeftLeg.Data)
	rightLeverage := stra.RightLeg.Broker.DataLeverage(stra.RightLeg.Data)

	// 当前仓位
	leftSize := stra.LeftLeg.Broker.GetDataSize(stra.LeftLeg.Data)
	rightSize := stra.RightLeg.Broker.GetDataSize(stra.RightLeg.Data)

	// 当前资金能开的最大仓位
	leftMaxSize, rightMaxSize, maxSize, onceOpenSize := CalcOnceOpenSize(stra.LeftLeg, stra.RightLeg, stra.Params, stra.Params.CapitalLimitLow, stra.openSize, leftPrice, rightPrice, leftLeverage, rightLeverage)

	// 订单后缀
	orderSuffix := strconv.FormatInt(nts, 10)

	// 盘口数据
	leftBooks := stra.LeftLeg.Data.BookNAll(5)
	leftBooksStr := fmt.Sprintf("%+v", leftBooks)
	rightBooks := stra.RightLeg.Data.BookNAll(5)
	rightBooksStr := fmt.Sprintf("%+v", rightBooks)

	// 开仓均价价差
	leftAvgPrice := stra.LeftLeg.Broker.GetAvgPrice(stra.LeftLeg.Data)
	rightAvgPrice := stra.RightLeg.Broker.GetAvgPrice(stra.RightLeg.Data)
	actualAvgDiff := 0.0
	if leftAvgPrice != 0 && rightAvgPrice != 0 {
		actualAvgDiff = (leftAvgPrice - rightAvgPrice) / rightAvgPrice * 100
	}

	// 左腿资金费率下限/周期, 百分数
	leftFundingFloor := stra.LeftLeg.Broker.FundingFloor(stra.LeftLeg.Data) * 100
	leftFundingPeriod := stra.LeftLeg.Data.FundingPeriod()
	leftFundingFloorHour := leftFundingFloor / leftFundingPeriod.Hours()
	// 右腿资金费率下限/周期, 百分数
	rightFundingFloor := stra.RightLeg.Broker.FundingFloor(stra.RightLeg.Data) * 100
	rightFundingPeriod := stra.RightLeg.Data.FundingPeriod()
	rightFundingFloorHour := rightFundingFloor / rightFundingPeriod.Hours()

	// 溢价指数和预测资金费
	leftPremiumIndex, leftPredictedFunding := PredictedFunding(stra.LeftLeg.Broker, stra.LeftLeg.Data)
	rightPremiumIndex, rightPredictedFunding := PredictedFunding(stra.RightLeg.Broker, stra.RightLeg.Data)

	// 检查是否需要市价下单（跨所套利通常使用限价单）
	leftUseMarket, rightUseMarket := false, false

	statuslogger := stra.logger.With(
		slog.Group(
			"价差",
			slog.String("最新价差", strconv.FormatFloat(diff, 'f', 6, 64)),
			slog.String("前n价差", strconv.FormatFloat(topDiff, 'f', 6, 64)),
			slog.String("后n价差", strconv.FormatFloat(bottomDiff, 'f', 6, 64)),
			slog.String("开仓均价价差", strconv.FormatFloat(actualAvgDiff, 'f', 6, 64)),
			slog.String("开仓价差", strconv.FormatFloat(stra.openDiff, 'f', 6, 64)),
			slog.String("平仓价差", strconv.FormatFloat(stra.closeDiff, 'f', 6, 64)),
			slog.String("右腿开仓价差", strconv.FormatFloat(stra.rightOpenDiff, 'f', 6, 64)),
			slog.String("右腿平仓价差", strconv.FormatFloat(stra.rightCloseDiff, 'f', 6, 64)),
		),
		slog.Group(
			"状态",
			slog.String("单次开仓仓位", strconv.FormatFloat(onceOpenSize, 'f', 6, 64)),
			slog.String("最大开仓仓位", strconv.FormatFloat(maxSize, 'f', 6, 64)),
			slog.String("触发时间", ntime),
			slog.String("任务当前状态", stra.status.String()),
			slog.String("订单后缀", orderSuffix),
			slog.String("剩余最小资金", strconv.FormatFloat(stra.Params.MinAvailable, 'f', 6, 64)),
			slog.String("资金分配上限", strconv.FormatFloat(stra.Params.CapitalLimitLow, 'f', 6, 64)),
		),
		slog.Group(
			"左腿",
			slog.String("类型", string(stra.LeftLeg.Data.DataType)),
			slog.String("交易所", stra.Params.LeftExchange),
			slog.String("价格", strconv.FormatFloat(leftPrice, 'f', 6, 64)),
			slog.String("价格指数", strconv.FormatFloat(stra.LeftLeg.Data.Index.Last(), 'f', 6, 64)),
			slog.String("期指价差", strconv.FormatFloat(leftIndexDiff, 'f', 6, 64)),
			slog.String("期指前n价差", strconv.FormatFloat(leftIndexTopDiff, 'f', 6, 64)),
			slog.String("期指后n价差", strconv.FormatFloat(leftIndexBottomDiff, 'f', 6, 64)),
			slog.String("盘口", leftBooksStr),
			slog.String("杠杆", strconv.FormatFloat(leftLeverage, 'f', 6, 64)),
			slog.String("最大仓位计算", strconv.FormatFloat(leftMaxSize, 'f', 6, 64)),
			slog.String("最大下单价格", strconv.FormatFloat(stra.LeftLeg.Data.MaxOrderPriceStrict(), 'f', 6, 64)),
			slog.String("最小下单价格", strconv.FormatFloat(stra.LeftLeg.Data.MinOrderPriceStrict(), 'f', 6, 64)),
			slog.String("下单限价更新时间", time.UnixMilli(stra.LeftLeg.Data.Info.PriceUpdateTime).In(stra.location).Format("2006-01-02 15:04:05.000 MST")),
			slog.String("仓位", strconv.FormatFloat(leftSize, 'f', 6, 64)),
			slog.String("开仓均价", strconv.FormatFloat(leftAvgPrice, 'f', 6, 64)),
			slog.Bool("挂单", stra.LeftLeg.Broker.HasPending(stra.LeftLeg.Data.Symbol)),
			slog.String("交易时间", leftTime),
			slog.Int64("时间差", nts-leftTs),
			slog.String("可用资金", strconv.FormatFloat(stra.LeftLeg.Broker.GetAvailable(stra.LeftLeg.Data), 'f', 6, 64)),
			slog.String("资金费率", strconv.FormatFloat(stra.LeftLeg.Data.Funding.Rate, 'f', 6, 64)),
			slog.String("资金费率上限", strconv.FormatFloat(stra.LeftLeg.Broker.FundingCap(stra.LeftLeg.Data), 'f', 6, 64)),
			slog.String("资金费率下限", strconv.FormatFloat(stra.LeftLeg.Broker.FundingFloor(stra.LeftLeg.Data), 'f', 6, 64)),
			slog.String("资金费率到期时间", time.UnixMilli(stra.LeftLeg.Data.Funding.Time).In(stra.location).Format("2006-01-02 15:04:05.000 MST")),
			slog.String("资金费率周期", stra.LeftLeg.Data.FundingPeriod().String()),
			slog.String("溢价指数", strconv.FormatFloat(leftPremiumIndex, 'f', 6, 64)),
			slog.String("预测资金费", strconv.FormatFloat(leftPredictedFunding, 'f', 6, 64)),
			slog.String("mmr", strconv.FormatFloat(stra.LeftLeg.Broker.GetMaintenanceMarginRate(), 'f', 6, 64)),
			slog.String("维持保证金", strconv.FormatFloat(stra.LeftLeg.Broker.Account.MaintMargin, 'f', 6, 64)),
			slog.String("权益", strconv.FormatFloat(stra.LeftLeg.Broker.Account.Equity, 'f', 6, 64)),
		),
		slog.Group(
			"右腿",
			slog.String("类型", string(stra.RightLeg.Data.DataType)),
			slog.String("交易所", stra.Params.RightExchange),
			slog.String("价格", strconv.FormatFloat(rightPrice, 'f', 6, 64)),
			slog.String("价格指数", strconv.FormatFloat(stra.RightLeg.Data.Index.Last(), 'f', 6, 64)),
			slog.String("期指价差", strconv.FormatFloat(rightIndexDiff, 'f', 6, 64)),
			slog.String("期指前n价差", strconv.FormatFloat(rightIndexTopDiff, 'f', 6, 64)),
			slog.String("期指后n价差", strconv.FormatFloat(rightIndexBottomDiff, 'f', 6, 64)),
			slog.String("盘口", rightBooksStr),
			slog.String("杠杆", strconv.FormatFloat(rightLeverage, 'f', 6, 64)),
			slog.String("最大仓位计算", strconv.FormatFloat(rightMaxSize, 'f', 6, 64)),
			slog.String("最大下单价格", strconv.FormatFloat(stra.RightLeg.Data.MaxOrderPriceStrict(), 'f', 6, 64)),
			slog.String("最小下单价格", strconv.FormatFloat(stra.RightLeg.Data.MinOrderPriceStrict(), 'f', 6, 64)),
			slog.String("下单限价更新时间", time.UnixMilli(stra.RightLeg.Data.Info.PriceUpdateTime).In(stra.location).Format("2006-01-02 15:04:05.000 MST")),
			slog.String("仓位", strconv.FormatFloat(rightSize, 'f', 6, 64)),
			slog.String("开仓均价", strconv.FormatFloat(rightAvgPrice, 'f', 6, 64)),
			slog.Bool("挂单", stra.RightLeg.Broker.HasPending(stra.RightLeg.Data.Symbol)),
			slog.String("交易时间", rightTime),
			slog.Int64("时间差", nts-rightTs),
			slog.String("可用资金", strconv.FormatFloat(stra.RightLeg.Broker.GetAvailable(stra.RightLeg.Data), 'f', 6, 64)),
			slog.String("资金费率", strconv.FormatFloat(stra.RightLeg.Data.Funding.Rate, 'f', 6, 64)),
			slog.String("资金费率上限", strconv.FormatFloat(stra.RightLeg.Broker.FundingCap(stra.RightLeg.Data), 'f', 6, 64)),
			slog.String("资金费率下限", strconv.FormatFloat(stra.RightLeg.Broker.FundingFloor(stra.RightLeg.Data), 'f', 6, 64)),
			slog.String("资金费率到期时间", time.UnixMilli(stra.RightLeg.Data.Funding.Time).In(stra.location).Format("2006-01-02 15:04:05.000 MST")),
			slog.String("资金费率周期", stra.RightLeg.Data.FundingPeriod().String()),
			slog.String("溢价指数", strconv.FormatFloat(rightPremiumIndex, 'f', 6, 64)),
			slog.String("预测资金费", strconv.FormatFloat(rightPredictedFunding, 'f', 6, 64)),
			slog.String("mmr", strconv.FormatFloat(stra.RightLeg.Broker.GetMaintenanceMarginRate(), 'f', 6, 64)),
			slog.String("维持保证金", strconv.FormatFloat(stra.RightLeg.Broker.Account.MaintMargin, 'f', 6, 64)),
			slog.String("权益", strconv.FormatFloat(stra.RightLeg.Broker.Account.Equity, 'f', 6, 64)),
		),
	)

	TimedDo(stra.statusTicker, func() { statuslogger.Info("Status") })

	// 数据源有效性判断, 超过1分钟未更新停止任务
	if nts-rightTs > 1000*60*10 || nts-leftTs > 1000*60*10 {
		stra.logger.Warn("数据源等待超时, 关闭任务", "leftTime", leftTime, "rightTime", rightTime)
		stra.Pause()
		return
	}

	// 撤销过期订单
	CancelInValidOrder(stra.LeftLeg, stra.RightLeg, stra.logger)

	// 持仓风险判断
	stra.status = MaintainMarginReduce(stra.LeftLeg, stra.RightLeg, stra.status, stra.notifyLogger)

	// mmr减仓
	MMRReduceClose(stra.LeftLeg, stra.RightLeg, stra.status, onceOpenSize, leftPrice, rightPrice, orderSuffix+"MMReduce", leftUseMarket, rightUseMarket, statuslogger)

	// 清仓
	ForceClose(stra.LeftLeg, stra.RightLeg, stra.status, onceOpenSize, leftPrice, rightPrice, orderSuffix+"ForceClose", leftUseMarket, rightUseMarket, statuslogger)

	openCond := onceOpenSize > 0 &&
		stra.status == strategy.Active &&
		min(diff, topDiff) > stra.openDiff &&
		// stra.RightLeg.Data.FundingPeriod() > 1*time.Hour &&
		leftFundingFloorHour > rightFundingFloorHour &&
		max(leftIndexDiff, leftIndexTopDiff) < leftFundingFloor &&
		max(rightIndexDiff, rightIndexTopDiff) < rightFundingFloor

	closeCond := stra.status <= strategy.Reduce &&
		(min(rightIndexDiff, rightIndexBottomDiff) > stra.rightCloseDiff ||
			rightPredictedFunding > stra.RightLeg.Broker.FundingFloor(stra.RightLeg.Data)/2 ||
			min(diff, bottomDiff) < stra.closeDiff)

	// 时间差值满足一定阈值, 且两条腿差距较小
	if nts-rightTs < 3000 &&
		nts-leftTs < 3000 &&
		math.Abs(stra.RightLeg.Broker.GetDataSize(stra.RightLeg.Data)+stra.LeftLeg.Broker.GetDataSize(stra.LeftLeg.Data)) < 2*onceOpenSize {

		// 当前价差小于平仓阈值, 且 状态可平仓
		if closeCond {
			OnceClose(stra.LeftLeg, stra.RightLeg, onceOpenSize, 5, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
		}

		// 当前价差大于开仓阈值 且 任务为Active
		if openCond && !closeCond {
			OnceOpen(stra.LeftLeg, stra.RightLeg, onceOpenSize, maxSize, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
		}

	}

	// 对齐仓位, 不受状态限制
	AlignPosition(stra.LeftLeg, stra.RightLeg, stra.cmpLeg, stra.baseLeg,
		openCond && !closeCond,
		closeCond,
		orderSuffix+"Align", leftUseMarket, rightUseMarket, statuslogger)

	// 左腿仓位检查
	LeftPosCheck(stra.LeftLeg, statuslogger)
	// 右腿仓位检查
	RightPosCheck(stra.RightLeg, statuslogger)

}
