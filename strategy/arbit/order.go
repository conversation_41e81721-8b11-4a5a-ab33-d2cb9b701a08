package arbit

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"fmt"
	"log/slog"
	"math"
	"sync"
)

// 下开仓单
func SubmitOpenOrder(
	lg, rg *ArbitLeg,
	position float64,
	lprice float64,
	rprice float64,
	orderSuffix string,
	leftUseMarket bool,
	rightUseMarket bool,
	logger *slog.Logger) (err error) {
	if position == 0 {
		// logger.Info("仓位已满")
		return nil
	}
	// 盘口深度阈值
	minBookDepth := 10.0
	// 右腿挂单检查
	switch lg.Data.DataType {
	case data.Spot:
		if o, exists := rg.Broker.LastMarginOrder(rg.Data.Symbol); exists && o.IsClose() {
			logger.Info("撤销右腿平仓杠杆挂单", "order", o)
			rg.Client.CancelAllMarginOrders(rg.Broker, rg.Data)
			return
		}
	case data.Futures:
		if o, exists := rg.Broker.LastFuturesOrder(rg.Data.Symbol); exists && o.IsClose() {
			logger.Info("撤销右腿平仓期货挂单", "order", o)
			rg.Client.CancelAllFutureOrders(rg.Broker, rg.Data)
			return
		}
	}
	// 左腿挂单检查
	switch lg.Data.DataType {
	case data.Spot:
		if o, exists := lg.Broker.LastMarginOrder(lg.Data.Symbol); exists && o.IsClose() {
			logger.Info("撤销左腿平仓杠杆挂单", "order", o)
			lg.Client.CancelAllMarginOrders(lg.Broker, lg.Data)
			return

		}
	case data.Futures:
		if o, exists := lg.Broker.LastFuturesOrder(lg.Data.Symbol); exists && o.IsClose() {
			logger.Info("撤销左腿平仓期货挂单", "order", o)
			lg.Client.CancelAllFutureOrders(lg.Broker, lg.Data)
			return
		}
	}

	leftSize := utils.RoundToStep(position, lg.Data.Info.StepSize*lg.Data.Info.CtVal)
	// leftOrderPrice := max(lg.Data.BidN(5).Price, lg.Data.MinOrderPriceStrict())
	// 优化为盘口20和当前盘口价格的千5偏离价格
	leftOrderPrice := max(lg.Data.BidN(20).Price, utils.RoundToStep(lg.Data.Bid1Price()*(1-0.005), lg.Data.Info.TickSize), lg.Data.MinOrderPriceStrict())

	// 左腿盘口深度检查
	leftBookSize := 0.0
	for _, b := range lg.Data.BidNAll(20) {
		if b.Price >= leftOrderPrice {
			leftBookSize += b.Size
		}
	}
	if leftBookSize*leftOrderPrice < minBookDepth {
		logger.Info(
			"左腿盘口开仓深度不足",
			"minBookDepth", minBookDepth,
			"bookSize", leftBookSize,
			"leftSize", leftSize,
			"leftPrice", lprice,
			"leftOrderPrice", leftOrderPrice,
			"bidBooks", fmt.Sprintf("%+v", lg.Data.BidNAll(5)),
		)
		return nil
	} else {
		// 如果盘口深度大于5, 则调整仓位
		leftSize = utils.RoundToStep(min(leftBookSize/2, position), lg.Data.Info.StepSize*lg.Data.Info.CtVal)
	}

	rightSize := utils.RoundToStep(position, rg.Data.Info.StepSize*rg.Data.Info.CtVal)
	// rightOrderPrice := min(rg.Data.AskN(5).Price, rg.Data.MaxOrderPriceStrict())
	rightOrderPrice := min(rg.Data.AskN(20).Price, utils.RoundToStep(rg.Data.Ask1Price()*(1+0.005), rg.Data.Info.TickSize), rg.Data.MaxOrderPriceStrict())

	// 右腿盘口深度检查
	rightBookSize := 0.0
	for _, b := range rg.Data.AskNAll(20) {
		if b.Price <= rightOrderPrice {
			rightBookSize += b.Size
		}
	}
	if rightBookSize*rightOrderPrice < minBookDepth {
		logger.Info(
			"右腿盘口开仓深度不足",
			"minBookDepth", minBookDepth,
			"bookSize", rightBookSize,
			"rightSize", rightSize,
			"rightPrice", rprice,
			"rightOrderPrice", rightOrderPrice,
			"askBooks", fmt.Sprintf("%+v", rg.Data.AskNAll(5)),
		)
		return nil
	} else {
		// 如果盘口深度大于5, 则调整仓位
		rightSize = utils.RoundToStep(min(rightBookSize/2, position), rg.Data.Info.StepSize*rg.Data.Info.CtVal)
	}

	// 统一左右腿仓位
	leftSize = min(leftSize, rightSize)
	rightSize = leftSize

	// 检查左腿开仓价格
	// 可能补充仓位过小, 导致下单失败, 检查最小订单价值, 如果过小则忽略
	llever := lg.Broker.DataLeverage(lg.Data)
	if leftOrderPrice*leftSize < lg.Data.Info.MinNotional ||
		// 余额需满足两倍开仓价值, 防止相同账户同时开仓导致开仓失败
		leftOrderPrice*leftSize/llever > 2*lg.Broker.GetAvailable(lg.Data) ||
		leftSize < lg.Data.Info.MinSize*lg.Data.Info.CtVal {
		logger.Info("左腿开仓仓位过小或大于可用资金",
			"orderValue", leftOrderPrice*leftSize,
			"左腿最小价值", lg.Data.Info.MinNotional,
			"左腿最小仓位", lg.Data.Info.MinSize*lg.Data.Info.CtVal,
			"可用资金", lg.Broker.GetAvailable(lg.Data))
		return nil
	}

	// 检查右腿开仓价格
	rlever := rg.Broker.DataLeverage(rg.Data)
	if rightOrderPrice*rightSize < rg.Data.Info.MinNotional ||
		// 余额需满足两倍开仓价值, 防止相同账户同时开仓导致开仓失败
		rightOrderPrice*rightSize/rlever > 2*rg.Broker.GetAvailable(rg.Data) ||
		rightSize < rg.Data.Info.MinSize*rg.Data.Info.CtVal {
		logger.Info("右腿开仓仓位过小或大于可用资金",
			"orderValue", rightOrderPrice*rightSize,
			"右腿最小价值", rg.Data.Info.MinNotional,
			"右腿最小仓位", rg.Data.Info.MinSize*rg.Data.Info.CtVal,
			"可用资金", rg.Broker.GetAvailable(rg.Data))
		return nil
	}

	leftOrderType := "限价"
	rightOrderType := "限价"
	if leftUseMarket {
		leftOrderType = "市价"
	}
	if rightUseMarket {
		rightOrderType = "市价"
	}

	logger.Info("左腿开仓下单", "下单价格", leftOrderPrice, "下单类型", leftOrderType, "tick", lg.Data.Info.TickSize, "仓位", leftSize)
	logger.Info("右腿开仓下单", "下单价格", rightOrderPrice, "下单类型", rightOrderType, "tick", rg.Data.Info.TickSize, "仓位", rightSize)

	wg := &sync.WaitGroup{}
	wg.Add(2)
	// 左腿下单
	go SubmitDataOrderWithType(lg.Client, lg.Broker, lg.Data, orderSuffix, leftOrderPrice, leftSize, order.SELL, leftUseMarket, wg, logger.With("leg", "left").With("orderReason", "open"))
	// 右腿下单
	go SubmitDataOrderWithType(rg.Client, rg.Broker, rg.Data, orderSuffix, rightOrderPrice, rightSize, order.BUY, rightUseMarket, wg, logger.With("leg", "right").With("orderReason", "open"))
	wg.Wait()
	return nil
}

// 下平仓单
func SubmitCloseOrder(
	lg, rg *ArbitLeg,
	position float64,
	full bool,
	lprice float64,
	rprice float64,
	orderSuffix string,
	leftUseMarket bool,
	rightUseMarket bool,
	logger *slog.Logger) (err error) {
	if position == 0 {
		// logger.Info("仓位已空")
		return nil
	}
	// 盘口深度阈值
	minBookDepth := 10.0
	// 右腿挂单检查
	switch lg.Data.DataType {
	case data.Spot:
		if o, exists := rg.Broker.LastMarginOrder(rg.Data.Symbol); exists && o.IsOpen() {
			logger.Info("撤销右腿开仓杠杆挂单", "order", o)
			rg.Client.CancelAllMarginOrders(rg.Broker, rg.Data)
			return
		}
	case data.Futures:
		if o, exists := rg.Broker.LastFuturesOrder(rg.Data.Symbol); exists && o.IsOpen() {
			logger.Info("撤销右腿开仓期货挂单", "order", o)
			rg.Client.CancelAllFutureOrders(rg.Broker, rg.Data)
			return
		}
	}
	// 左腿挂单检查
	switch lg.Data.DataType {
	case data.Spot:
		if o, exists := lg.Broker.LastMarginOrder(lg.Data.Symbol); exists && o.IsOpen() {
			logger.Info("撤销左腿开仓杠杆挂单", "order", o)
			lg.Client.CancelAllMarginOrders(lg.Broker, lg.Data)
			return
		}
	case data.Futures:
		if o, exists := lg.Broker.LastFuturesOrder(lg.Data.Symbol); exists && o.IsOpen() {
			logger.Info("撤销左腿开仓期货挂单", "order", o)
			lg.Client.CancelAllFutureOrders(lg.Broker, lg.Data)
			return
		}
	}

	leftSize := utils.RoundToStep(position, lg.Data.Info.StepSize*lg.Data.Info.CtVal)
	// 盘口20和当前盘口价格的千5偏离价格
	leftOrderPrice := min(lg.Data.AskN(20).Price, utils.RoundToStep(lg.Data.Ask1Price()*(1+0.005), lg.Data.Info.TickSize), lg.Data.MaxOrderPriceStrict())

	// 左腿盘口深度检查
	leftBookSize := 0.0
	for _, b := range lg.Data.AskNAll(20) {
		if b.Price <= leftOrderPrice {
			leftBookSize += b.Size
		}
	}
	// 深度过小则尝试市价平仓
	if leftBookSize*leftOrderPrice < minBookDepth {
		logger.Info(
			"左腿盘口平仓深度不足, 尝试市价平仓",
			"minBookDepth", minBookDepth,
			"bookSize", leftBookSize,
			"leftSize", leftSize,
			"leftPrice", lprice,
			"leftOrderPrice", leftOrderPrice,
			"askBooks", fmt.Sprintf("%+v", lg.Data.AskNAll(5)),
		)
		leftUseMarket = true
	}

	rightSize := utils.RoundToStep(position, rg.Data.Info.StepSize*rg.Data.Info.CtVal)
	// 盘口20和当前盘口价格的千5偏离价格
	rightOrderPrice := max(rg.Data.BidN(20).Price, utils.RoundToStep(rg.Data.Bid1Price()*(1-0.005), rg.Data.Info.TickSize), rg.Data.MinOrderPriceStrict())

	// 右腿盘口深度检查
	rightBookSize := 0.0
	for _, b := range rg.Data.BidNAll(20) {
		if b.Price >= rightOrderPrice {
			rightBookSize += b.Size

		}
	}
	// 度过小则尝试市价平仓
	if rightBookSize*rightOrderPrice < minBookDepth {
		logger.Info(
			"右腿盘口平仓深度不足, 尝试市价平仓",
			"minBookDepth", minBookDepth,
			"bookSize", rightBookSize,
			"rightSize", rightSize,
			"rightPrice", rprice,
			"rightOrderPrice", rightOrderPrice,
			"bidBooks", fmt.Sprintf("%+v", rg.Data.BidNAll(5)),
		)
		rightUseMarket = true
	}

	// 统一左右腿仓位
	leftSize = min(leftSize, rightSize)
	rightSize = leftSize

	// 如果全平仓, 则尝试获取所有仓位
	if full {
		leftSize = math.Abs(lg.Broker.GetDataSize(lg.Data))
		rightSize = math.Abs(rg.Broker.GetDataSize(rg.Data))
	}

	// 检查左腿平仓价格
	if leftOrderPrice*leftSize < lg.Data.Info.MinNotional ||
		leftSize < lg.Data.Info.MinSize*lg.Data.Info.CtVal {
		logger.Info("左腿平仓仓位过小",
			"orderValue", leftOrderPrice*leftSize,
			"左腿最小仓位", lg.Data.Info.MinSize*lg.Data.Info.CtVal,
			"左腿最小价值", lg.Data.Info.MinNotional)
		return nil
	}

	// 检查右腿平仓价格
	if rightOrderPrice*rightSize < rg.Data.Info.MinNotional ||
		leftSize < rg.Data.Info.MinSize*rg.Data.Info.CtVal {
		logger.Info("右腿平仓仓位过小",
			"orderValue", rightOrderPrice*rightSize,
			"右腿最小仓位", rg.Data.Info.MinSize*rg.Data.Info.CtVal,
			"右腿最小价值", rg.Data.Info.MinNotional)
		return nil
	}

	leftOrderType := "限价"
	rightOrderType := "限价"
	if leftUseMarket {
		leftOrderType = "市价"
	}
	if rightUseMarket {
		rightOrderType = "市价"
	}

	logger.Info("左腿平仓下单", "下单价格", leftOrderPrice, "下单类型", leftOrderType, "tick", lg.Data.Info.TickSize, "仓位", leftSize)
	logger.Info("右腿平仓下单", "下单价格", rightOrderPrice, "下单类型", rightOrderType, "tick", rg.Data.Info.TickSize, "仓位", rightSize)
	wg := &sync.WaitGroup{}
	wg.Add(2)
	// 左腿下单
	go SubmitDataOrderWithType(lg.Client, lg.Broker, lg.Data, orderSuffix, leftOrderPrice, leftSize, order.BUY, leftUseMarket, wg, logger.With("leg", "left").With("orderReason", "close"))
	// 右腿下单
	go SubmitDataOrderWithType(rg.Client, rg.Broker, rg.Data, orderSuffix, rightOrderPrice, rightSize, order.SELL, rightUseMarket, wg, logger.With("leg", "right").With("orderReason", "close"))
	wg.Wait()
	return nil
}

// // 根据数据源类型提交订单
// func SubmitDataOrder(
// 	c client.APIClient, b *broker.Broker, d *data.SymbolData, suffix string, price float64, size float64, side order.Side, useMarket bool,
// 	wg *sync.WaitGroup, logger *slog.Logger,
// ) (err error) {
// 	defer func() {
// 		if wg != nil {
// 			wg.Done()
// 		}
// 	}()
// 	o := order.CreateOrder().SetSymbol(d.Symbol).SetClOrdId(suffix).
// 		SetSize(size).SetSide(side).SetTimeInForce(order.IOC).SetValidSec(5)

// 	if useMarket {
// 		o.SetOrdType(order.MARKET)
// 	} else {
// 		o.SetOrdType(order.LIMIT).SetPrice(price)
// 	}
// 	_, err = c.SubmitOrder(b, o, d)
// 	if err != nil {
// 		// 打印错误
// 		logger.Warn("下单失败", "dataType", d.DataType, "order", o, "error", err)
// 		// 判断err是否包含余额不足
// 		if err.Error() == "margin" {
// 			// api查询当前余额
// 			accountInfo, _ := c.QueryAccount(b)
// 			logger.Warn("下单失败, 余额不足, 检查交易所余额", "dataType", d.DataType, "order", o, "accountInfo", accountInfo, "brokerAvailable", b.GetAvailable(d))
// 		}
// 		return err
// 	}
// 	// 打印订单
// 	logger.Info("下单成功", "dataType", d.DataType, "order", o)
// 	return nil
// }

// 根据数据源类型提交订单（支持指定订单类型）
func SubmitDataOrderWithType(
	c client.APIClient, b *broker.Broker, d *data.SymbolData, suffix string, price float64, size float64, side order.Side,
	isMarket bool, wg *sync.WaitGroup, logger *slog.Logger,
) (err error) {
	defer func() {
		if wg != nil {
			wg.Done()
		}
	}()

	var o *order.Order
	if isMarket {
		// 市价单
		o = order.CreateOrder().SetSymbol(d.Symbol).SetClOrdId(suffix).
			SetSize(size).SetSide(side).SetOrdType(order.MARKET).SetTimeInForce(order.GTC).SetValidSec(5)
	} else {
		// 限价单
		o = order.CreateOrder().SetSymbol(d.Symbol).SetClOrdId(suffix).SetPrice(price).
			SetSize(size).SetSide(side).SetOrdType(order.LIMIT).SetTimeInForce(order.IOC).SetValidSec(5)
	}

	_, err = c.SubmitOrder(b, o, d)
	if err != nil {
		// 打印错误
		logger.Warn("下单失败", "dataType", d.DataType, "order", o, "error", err)
		// 判断err是否包含余额不足
		if err.Error() == "margin" {
			// api查询当前余额
			accountInfo, _ := c.QueryAccount(b)
			logger.Warn("下单失败, 余额不足, 检查交易所余额", "dataType", d.DataType, "order", o, "accountInfo", accountInfo, "brokerAvailable", b.GetAvailable(d))
		}
		return err
	}
	// 打印订单
	orderType := "限价"
	if isMarket {
		orderType = "市价"
	}
	logger.Info("下单成功", "dataType", d.DataType, "orderType", orderType, "order", o)
	return nil
}

// 仓位对齐
func AlignPosition(
	lg, rg *ArbitLeg,
	cmpLeg, baseLeg *ArbitLeg,
	openCond, closeCond bool,
	orderSuffix string,
	leftUseMarket, rightUseMarket bool,
	logger *slog.Logger,
) (tried bool) {
	cmpPos := cmpLeg.Broker.GetDataSize(cmpLeg.Data)
	basePos := baseLeg.Broker.GetDataSize(baseLeg.Data)
	logger = logger.With("basePos", basePos, "cmpPos", cmpPos)

	cmpUseMarket := leftUseMarket
	baseUseMarket := rightUseMarket
	if baseLeg == lg {
		cmpUseMarket = rightUseMarket
		baseUseMarket = leftUseMarket
	}

	// 有订单时, 等待结算
	if baseLeg.Broker.HasPending(baseLeg.Data.Symbol) || cmpLeg.Broker.HasPending(cmpLeg.Data.Symbol) {
		return false
	}

	// 仓位差小于最小开仓量
	if math.Abs(cmpPos+basePos) < cmpLeg.Data.Info.StepSize*cmpLeg.Data.Info.CtVal {
		return false
	}

	// 计算订单价值, 用基准组的买一价估算
	baseLever := baseLeg.Broker.Leverages[baseLeg.Data.Symbol]
	cmpLever := cmpLeg.Broker.Leverages[cmpLeg.Data.Symbol]
	baseOrderValue := baseLeg.Data.Ask1Price() * (basePos + cmpPos) / float64(baseLever)
	cmpOrderValue := baseLeg.Data.Ask1Price() * (basePos + cmpPos) / float64(cmpLever)
	baseAvailable := baseLeg.Broker.GetAvailable(baseLeg.Data)
	cmpAvailable := cmpLeg.Broker.GetAvailable(cmpLeg.Data)

	// 基准仓位sell单条件
	// 1. 价差大于开仓阈值, 基准仓位小, 基准仓位做空, 基准仓位余额充足
	// 2. 价差小于平仓阈值, 基准仓位大, 基准仓位做多
	// 3. 仓位多买, 基准仓位做多, 比较仓位余额不足2倍
	if (openCond && math.Abs(cmpPos) > math.Abs(basePos) && baseLeg == lg && baseAvailable > 2*baseOrderValue) ||
		(closeCond && math.Abs(cmpPos) < math.Abs(basePos) && baseLeg == rg) ||
		(cmpPos+basePos > 0 && baseLeg == rg && cmpAvailable < cmpOrderValue*2) {
		// 下sell单补齐仓位
		size := math.Abs(utils.RoundToStep(basePos+cmpPos, baseLeg.Data.Info.StepSize*baseLeg.Data.Info.CtVal))
		orderPrice := max(baseLeg.Data.BidN(5).Price, baseLeg.Data.MinOrderPriceStrict())
		if orderPrice*size < baseLeg.Data.Info.MinNotional ||
			size < baseLeg.Data.Info.StepSize*baseLeg.Data.Info.CtVal ||
			size < baseLeg.Data.Info.MinSize*baseLeg.Data.Info.CtVal {
			return false
		}
		logger.Info("基准组对齐仓位卖单下单", "价格", orderPrice, "tick", baseLeg.Data.Info.TickSize, "仓位", size)
		SubmitDataOrderWithType(baseLeg.Client, baseLeg.Broker, baseLeg.Data, orderSuffix, orderPrice, size, order.SELL, baseUseMarket, nil,
			logger.With("leg", "base").With("orderReason", "align"))
		return true
	}
	// 基准仓位buy单条件
	// 1. 价差大于开仓阈值, 基准仓位小, 基准仓位做多, 基准仓位余额充足
	// 2. 价差小于平仓阈值, 基准仓位大, 基准仓位做空
	// 3. 仓位多卖, 基准仓位做空, 比较仓位余额不足2倍
	if (openCond && math.Abs(cmpPos) > math.Abs(basePos) && baseLeg == rg && baseAvailable > 2*baseOrderValue) ||
		(closeCond && math.Abs(cmpPos) < math.Abs(basePos) && baseLeg == lg) ||
		(cmpPos+basePos < 0 && baseLeg == lg && cmpAvailable < cmpOrderValue*2) {
		// 下buy单补齐仓位
		size := math.Abs(utils.RoundToStep(basePos+cmpPos, baseLeg.Data.Info.StepSize*baseLeg.Data.Info.CtVal))
		orderPrice := min(baseLeg.Data.AskN(5).Price, baseLeg.Data.MaxOrderPriceStrict())
		if orderPrice*size < baseLeg.Data.Info.MinNotional ||
			size < baseLeg.Data.Info.StepSize*baseLeg.Data.Info.CtVal ||
			size < baseLeg.Data.Info.MinSize*baseLeg.Data.Info.CtVal {
			return false
		}

		logger.Info("基准组对齐仓位买单下单", "价格", orderPrice, "tick", baseLeg.Data.Info.TickSize, "仓位", size)
		SubmitDataOrderWithType(baseLeg.Client, baseLeg.Broker, baseLeg.Data, orderSuffix, orderPrice, size, order.BUY, baseUseMarket, nil,
			logger.With("leg", "base").With("orderReason", "align"))
		return true
	}

	// 仓位多买, 调整对照组
	if cmpPos+basePos > 0 {
		// 下sell单补齐仓位
		size := math.Abs(utils.RoundToStep(basePos+cmpPos, cmpLeg.Data.Info.StepSize*cmpLeg.Data.Info.CtVal))
		orderPrice := max(cmpLeg.Data.BidN(5).Price, cmpLeg.Data.MinOrderPriceStrict())
		if orderPrice*size < cmpLeg.Data.Info.MinNotional ||
			size < cmpLeg.Data.Info.StepSize*cmpLeg.Data.Info.CtVal ||
			size < cmpLeg.Data.Info.MinSize*cmpLeg.Data.Info.CtVal {
			return false
		}
		logger.Info("对照组对齐仓位卖单下单", "价格", orderPrice, "tick", cmpLeg.Data.Info.TickSize, "仓位", size)
		SubmitDataOrderWithType(cmpLeg.Client, cmpLeg.Broker, cmpLeg.Data, orderSuffix, orderPrice, size, order.SELL, cmpUseMarket, nil,
			logger.With("leg", "cmp").With("orderReason", "align"))
		return true
	}

	// 仓位多卖, 调整对照组
	if cmpPos+basePos < 0 {
		// 下buy单补齐仓位
		size := math.Abs(utils.RoundToStep(basePos+cmpPos, cmpLeg.Data.Info.StepSize*cmpLeg.Data.Info.CtVal))
		orderPrice := min(cmpLeg.Data.AskN(5).Price, cmpLeg.Data.MaxOrderPriceStrict())
		if orderPrice*size < cmpLeg.Data.Info.MinNotional ||
			size < cmpLeg.Data.Info.StepSize*cmpLeg.Data.Info.CtVal ||
			size < cmpLeg.Data.Info.MinSize*cmpLeg.Data.Info.CtVal {
			return false
		}
		logger.Info("对照组对齐仓位买单下单", "价格", orderPrice, "tick", cmpLeg.Data.Info.TickSize, "仓位", size)
		SubmitDataOrderWithType(cmpLeg.Client, cmpLeg.Broker, cmpLeg.Data, orderSuffix, orderPrice, size, order.BUY, cmpUseMarket, nil,
			logger.With("leg", "cmp").With("orderReason", "align"))
		return true
	}
	// 对照组仓位等于基准组仓位
	if cmpPos+basePos == 0 {
		return false
	}
	// 未知情况, 打印警告
	logger.Warn("仓位调整未知情况", "对照组仓位", cmpPos, "基准组仓位", basePos)
	return false
}

// 单次开仓
func OnceOpen(
	lg, rg *ArbitLeg,
	onceOpenSize, maxSize, leftPrice, rightPrice float64,
	orderSuffix string,
	leftUseMarket, rightUseMarket bool,
	statuslogger *slog.Logger,
) {
	// 计算开仓仓位
	calcOpenPosition := min(onceOpenSize, maxSize-rg.Broker.GetDataSize(rg.Data), maxSize-lg.Broker.GetDataSize(lg.Data))
	// 确保大于单次开仓量
	if calcOpenPosition >= onceOpenSize {
		SubmitOpenOrder(lg, rg, calcOpenPosition, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
	}
}

// 单次平仓
func OnceClose(
	lg, rg *ArbitLeg,
	onceOpenSize, multiple, leftPrice, rightPrice float64,
	orderSuffix string,
	leftUseMarket, rightUseMarket bool,
	statuslogger *slog.Logger,
) {
	full := false // 是否全平
	currSize := min(math.Abs(lg.Broker.GetDataSize(lg.Data)), math.Abs(rg.Broker.GetDataSize(rg.Data)))
	// 有仓位
	if currSize > 0 {
		// 如果剩余仓位不足指定倍数单次开仓量, 则平仓剩余仓位
		if currSize < (multiple+1)*onceOpenSize {
			full = true
			statuslogger.Info("try to close position all", "currSize", currSize, "onceOpenSize", onceOpenSize, "multiple", multiple)
		}
		SubmitCloseOrder(lg, rg, onceOpenSize*multiple, full, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
	}
}

// MMR减仓
func MMRReduceClose(
	lg, rg *ArbitLeg,
	status strategy.TaskStatus,
	onceOpenSize, leftPrice, rightPrice float64,
	orderSuffix string,
	leftUseMarket, rightUseMarket bool,
	statuslogger *slog.Logger,
) {
	if status == strategy.MMRClosing {
		// 根据当前mmr和预期mmr计算需要平掉的仓位
		leftMMR := lg.Broker.GetMaintenanceMarginRate()
		rightMMR := rg.Broker.GetMaintenanceMarginRate()
		// 安全mmr
		targetMMR := 5.0
		// 按照所需仓位的1/3平仓
		leftCloseSize, rightCloseSize := 0.0, 0.0
		if leftMMR < targetMMR {
			leftCloseSize = math.Abs((targetMMR - leftMMR) / targetMMR * lg.Broker.GetDataSize(lg.Data) / 3)
		}
		if rightMMR < targetMMR {
			rightCloseSize = math.Abs((targetMMR - rightMMR) / targetMMR * rg.Broker.GetDataSize(rg.Data) / 3)
		}
		closeSize := max(leftCloseSize, rightCloseSize)
		// 计算需要平仓的倍数
		multi := math.Ceil(closeSize / onceOpenSize)
		// 平仓
		OnceClose(lg, rg, onceOpenSize, multi, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
	}
}

// 清仓
func ForceClose(
	lg, rg *ArbitLeg,
	status strategy.TaskStatus,
	onceOpenSize, leftPrice, rightPrice float64,
	orderSuffix string,
	leftUseMarket, rightUseMarket bool,
	statuslogger *slog.Logger,
) {
	if status == strategy.Stop {
		OnceClose(lg, rg, onceOpenSize, 1, leftPrice, rightPrice, orderSuffix, leftUseMarket, rightUseMarket, statuslogger)
	}
}
