package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"maps"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	SymbolTaskMap := map[string]*arbit.DiffRedis{}

	// 任务log
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	taskName := "BnMarket"

	logDir := config.LogDir

	clientPath := filepath.Join(logDir, taskName, "client.log")
	clientFileWriter := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 10,         // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFileWriter.Close()
	// client logger
	clientLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(clientFileWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 客户端
	bndc, bnac, err := factory.NewBnDataClients(ctx, false, logger, clientLogger)
	if err != nil {
		logger.Error(err.Error())
		return
	}

	accountName := "TestLiquidCostom"
	account := bn.BnAccounts[accountName]
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Costom, nil)

	// signal
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 保存价差redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoDiffDB,
	})
	redisCtx := &strategy.RedisCtx{Client: rdb, Table: config.BnDiffTable}

	hsetChan := make(chan map[string]string, 1000)

	// 定时接受channel信息批量写入
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-signalChan:
				return
			case <-ticker.C:
				hsetData := map[string]string{}

				// 不设固定循环次数，直到 hsetChan 为空
				for {
					select {
					case data := <-hsetChan:
						maps.Copy(hsetData, data)
					default:
						// chan 为空，退出循环
						goto WriteRedis
					}
				}

			WriteRedis:
				if len(hsetData) > 0 {
					ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
					err := rdb.HSet(ctx, redisCtx.Table, hsetData).Err()
					cancel()
					if err != nil {
						logger.Warn("write redis failed", "error", err)
					} else {
						logger.Info("write redis success")
					}
				}
			}
		}
	}()

	// 定时运行一次
	ticker := time.NewTicker(time.Second * 60)

	for {
		okFuturesSymbols := bnac.QueryAllTradingFutures()
		okSpotSymbols := bnac.QueryAllTradingMargin(b)
		// 交集
		symbols := utils.Intersect(okFuturesSymbols, okSpotSymbols)
		// 转换为map
		symbolMap := utils.SliceToMap(symbols)

		logger.Info("symbols", "symbols", len(symbols))

		// 删除redis中不存在的币种
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		redisSymbols, err := rdb.HGetAll(ctx, redisCtx.Table).Result()
		cancel()
		if err != nil {
			logger.Error("获取redis中币种失败", "error", err)
			continue
		}
		for SymbolName, _ := range redisSymbols {
			if _, ok := symbolMap[SymbolName]; !ok {
				logger.Info("删除redis中不存在的币种", "symbol", SymbolName)
				ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
				err := rdb.HDel(ctx, redisCtx.Table, SymbolName).Err()
				cancel()
				if err != nil {
					logger.Error("删除redis中不存在的币种失败", "error", err)
				}
			}
		}

		// 未存在任务的币种添加任务
		// 预备订阅的数据源
		oksubds := []*data.SymbolData{}
		for SymbolName, _ := range symbolMap {
			if _, ok := SymbolTaskMap[SymbolName]; !ok {

				// 统一订阅数据
				fd := data.NewSymbolData(SymbolName, data.Futures)
				sd := data.NewSymbolData(SymbolName, data.Spot)

				oksubds = append(oksubds, fd, sd)
				// 策略logger
				straPath := filepath.Join(logDir, taskName, SymbolName+".log")
				straFile := &lumberjack.Logger{
					Filename:   straPath, // 日志文件的位置
					MaxSize:    10,       // 文件最大尺寸（以MB为单位）
					MaxBackups: 10,       // 保留的最大旧文件数量
					MaxAge:     7,        // 保留旧文件的最大天数
					Compress:   true,     // 是否压缩/归档旧文件
					LocalTime:  true,     // 使用本地时间创建时间戳
				}
				defer straFile.Close()
				straLogger := slog.New(slog.NewJSONHandler(straFile, &slog.HandlerOptions{
					Level: slog.LevelInfo,
				}))

				stra := arbit.NewDiffRedis(fd, sd, redisCtx, hsetChan, straLogger)
				logger.Info("Start task", "symbol", SymbolName)
				stra.Start()

				SymbolTaskMap[SymbolName] = stra
			}
		}

		// 订阅数据
		if len(oksubds) > 0 {
			bndc.SubscribeDatas(oksubds)
			bnac.UpdateDatas(oksubds)
		}

		// 任务中未存在的币种停止任务
		for SymbolName, store := range SymbolTaskMap {
			if _, ok := symbolMap[SymbolName]; !ok {
				logger.Info("Stop task", "symbol", SymbolName)
				store.Stop()
				delete(SymbolTaskMap, SymbolName)
			}
		}

		// 任务中已经停止的任务删除, 等待下次重新订阅
		for SymbolName, store := range SymbolTaskMap {
			if !store.Running() {
				logger.Info("Task is not running, delete it", "symbol", SymbolName)
				delete(SymbolTaskMap, SymbolName)
			}
		}

		// 结束进程
		select {
		case <-signalChan:
			logger.Info("Received signal to exit")
			for _, stra := range SymbolTaskMap {
				if stra != nil {
					stra.Stop()
				}
			}
			return
		case <-ticker.C:
			continue
		}
	}
}
