package main

import (
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

type DiffInfo struct {
	Diff                    float64 `json:"价差"`
	FundingCost             float64 `json:"资金费成本"`
	LeftFundingPeriodHours  float64 `json:"左腿资金费周期"`
	RightFundingPeriodHours float64 `json:"右腿资金费周期"`
	LeftPreOffline          bool    `json:"左腿预下线"`
	RightPreOffline         bool    `json:"右腿预下线"`
}

// Redis查询参数结构体
type DiffQueryParams struct {
	DiffRdb        *redis.Client // 价差数据redis客户端
	DiffTable      string        // 价差数据表名
	LeftMetaTable  string        // 左腿元信息表名
	RightMetaTable string        // 右腿元信息表名
	Negative       bool          // 是否反向套利
	MetaRdb        *redis.Client // 元信息redis客户端
	Logger         *slog.Logger  // 日志记录器
}

// 筛选函数类型：输入价差信息，返回是否通过筛选
type FilterFunc func(info DiffInfo) bool

// 品种元信息结构体
type SymbolMeta struct {
	Symbol        string `json:"symbol"`
	SymbolOri     string `json:"symbol_ori"`
	OnlineTs      int64  `json:"online_ts"`
	OfflineTs     int64  `json:"offline_ts"`
	IsAddTask     string `json:"is_add_task"`
	RuleType      string `json:"rule_type"`
	IsMarginAllow bool   `json:"is_margin_allow"`
	Trading       bool   `json:"trading"`
	PreOffline    bool   `json:"pre_offline"`
	Ctval         int    `json:"ctval"`
	PricePrec     int    `json:"price_prec"`
	QuantityPrec  int    `json:"quantity_prec"`
}

func getRedisDiff(params DiffQueryParams) (largeSymbols map[string]DiffInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	// 批量获取价差数据
	crossDiffs, err := params.DiffRdb.HGetAll(ctx, params.DiffTable).Result()
	if err != nil {
		params.Logger.Info("获取价差失败", "error", err)
		return largeSymbols
	}

	// 批量获取元信息数据
	leftMetaMap, err := params.MetaRdb.HGetAll(ctx, params.LeftMetaTable).Result()
	if err != nil {
		params.Logger.Debug("获取左腿元信息失败", "table", params.LeftMetaTable, "error", err)
		leftMetaMap = make(map[string]string) // 创建空map避免panic
	}

	rightMetaMap, err := params.MetaRdb.HGetAll(ctx, params.RightMetaTable).Result()
	if err != nil {
		params.Logger.Debug("获取右腿元信息失败", "table", params.RightMetaTable, "error", err)
		rightMetaMap = make(map[string]string) // 创建空map避免panic
	}

	// 解析左右腿元信息到分别的map中
	leftSymbolMetaMap := make(map[string]SymbolMeta)
	rightSymbolMetaMap := make(map[string]SymbolMeta)

	// 处理左腿元信息
	for symbol, metaJson := range leftMetaMap {
		var meta SymbolMeta
		if err := json.Unmarshal([]byte(metaJson), &meta); err == nil {
			leftSymbolMetaMap[symbol] = meta
		}
	}

	// 处理右腿元信息
	for symbol, metaJson := range rightMetaMap {
		var meta SymbolMeta
		if err := json.Unmarshal([]byte(metaJson), &meta); err == nil {
			rightSymbolMetaMap[symbol] = meta
		}
	}

	largeSymbols = make(map[string]DiffInfo)
	for symbol, infoJson := range crossDiffs {
		// 检查任务是否过期
		var info map[string]string
		json.Unmarshal([]byte(infoJson), &info)
		diff, _ := strconv.ParseFloat(info["diff_rate"], 64)
		fundCost, _ := strconv.ParseFloat(info["funding_cost"], 64)
		leftFundingPeriodHours, _ := strconv.ParseFloat(info["left_funding_period"], 64)
		rightFundingPeriodHours, _ := strconv.ParseFloat(info["right_funding_period"], 64)

		leftTs, _ := strconv.ParseInt(info["left_time"], 10, 64)
		rightTs, _ := strconv.ParseInt(info["right_time"], 10, 64)
		ts := min(leftTs, rightTs)
		// ms时间戳距离当前时间不超过10min，只做时间过期筛选
		if time.Now().UnixMilli()-ts < 10*60*1000 {
			// 从预加载的元信息map中分别获取左右腿信息
			leftMeta, leftExists := leftSymbolMetaMap[symbol]
			rightMeta, rightExists := rightSymbolMetaMap[symbol]

			// 如果没有找到元信息，使用默认值
			if !leftExists {
				leftMeta = SymbolMeta{}
			}
			if !rightExists {
				rightMeta = SymbolMeta{}
			}

			if params.Negative {
				// 反向套利：左右腿位置互换，价差和资金费成本取反
				largeSymbols[symbol] = DiffInfo{
					Diff:                    -diff,
					FundingCost:             -fundCost,
					LeftFundingPeriodHours:  rightFundingPeriodHours,
					RightFundingPeriodHours: leftFundingPeriodHours,
					LeftPreOffline:          rightMeta.PreOffline, // 反向时左右腿互换
					RightPreOffline:         leftMeta.PreOffline,
				}
			} else {
				// 正向套利：保持原有位置
				largeSymbols[symbol] = DiffInfo{
					Diff:                    diff,
					FundingCost:             fundCost,
					LeftFundingPeriodHours:  leftFundingPeriodHours,
					RightFundingPeriodHours: rightFundingPeriodHours,
					LeftPreOffline:          leftMeta.PreOffline,
					RightPreOffline:         rightMeta.PreOffline,
				}
			}
		}
	}
	return largeSymbols
}

// 通用筛选函数
func filterSymbols(symbols map[string]DiffInfo, filterFunc FilterFunc) map[string]DiffInfo {
	filtered := make(map[string]DiffInfo)
	for symbol, info := range symbols {
		if filterFunc(info) {
			filtered[symbol] = info
		}
	}
	return filtered
}

// 预定义筛选函数

// 基础价差筛选：价差大于0.5%
var DiffLowFilter FilterFunc = func(info DiffInfo) bool {
	return info.Diff > 0.5 && !info.LeftPreOffline && !info.RightPreOffline
}

// 价差大于0.5%且左腿周期大于等于右腿周期
var DiffLowPeriodFilter FilterFunc = func(info DiffInfo) bool {
	return info.Diff > 0.5 && info.LeftFundingPeriodHours >= info.RightFundingPeriodHours && !info.LeftPreOffline && !info.RightPreOffline
}

// 大价差筛选：价差大于2.5%
var DiffHighFilter FilterFunc = func(info DiffInfo) bool {
	return info.Diff > 2 && !info.LeftPreOffline && !info.RightPreOffline
}

// 价差大于2.5%且左腿周期大于等于右腿周期
var DiffHighPeriodFilter FilterFunc = func(info DiffInfo) bool {
	return info.Diff > 2 && info.LeftFundingPeriodHours >= info.RightFundingPeriodHours && !info.LeftPreOffline && !info.RightPreOffline
}
