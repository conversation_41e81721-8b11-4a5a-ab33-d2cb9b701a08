package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/utils"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

// 价差通知程序

func main() {
	// redis
	tokyoRdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoDiffDB,
	})

	hkRdb := redis.NewClient(&redis.Options{
		Addr:     config.HKRedisAddr,
		Password: config.HKRedisPassword,
		DB:       config.HKDiffDB,
	})

	metaRdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.MetaDB,
	})

	// feishuWriter := logs.NewFeishuWriter(
	// 	[]string{},
	// 	config.DiffNotify.Hook,
	// 	config.DiffNotify.Secret,
	// 	"价差通知",
	// 	slog.LevelInfo,
	// )

	// logger := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, feishuWriter), nil))

	logger := utils.StdoutLogger

	// 创建电话通知客户端
	alertClient := utils.NewAlertClient(logger)

	// 先执行一次
	queryDiff(tokyoRdb, hkRdb, metaRdb, logger, alertClient)
	// 10分钟执行一次
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		queryDiff(tokyoRdb, hkRdb, metaRdb, logger, alertClient)
	}
}

// 格式化币种信息用于电话通知
func formatSymbolsForAlert(symbols map[string]DiffInfo) string {
	if len(symbols) == 0 {
		return ""
	}

	var details []string
	for symbol, info := range symbols {
		detail := fmt.Sprintf("%s(价差:%.2f%%, 左腿周期:%.1fh, 右腿周期:%.1fh)",
			symbol, info.Diff, info.LeftFundingPeriodHours, info.RightFundingPeriodHours)
		details = append(details, detail)
	}

	// 限制显示的币种数量，避免通知内容过长
	if len(details) > 5 {
		return fmt.Sprintf("%s等%d个交易对", strings.Join(details[:5], ", "), len(details))
	}
	return strings.Join(details, ", ")
}

func queryDiff(tokyoRdb *redis.Client, hkRdb *redis.Client, metaRdb *redis.Client, logger *slog.Logger, alertClient *utils.AlertClient) {
	// 预设查询参数

	// 正向跨所配置参数
	crossDiffPositiveParams := DiffQueryParams{
		DiffRdb:        tokyoRdb,
		DiffTable:      config.CrossDiffTable,
		LeftMetaTable:  config.BnFuturesMetaInfo,
		RightMetaTable: config.OkFuturesMetaInfo,
		Negative:       false,
		MetaRdb:        metaRdb,
		Logger:         logger,
	}

	// 反向跨所配置参数
	crossDiffNegativeParams := DiffQueryParams{
		DiffRdb:        tokyoRdb,
		DiffTable:      config.CrossDiffTable,
		LeftMetaTable:  config.BnFuturesMetaInfo,
		RightMetaTable: config.OkFuturesMetaInfo,
		Negative:       true,
		MetaRdb:        metaRdb,
		Logger:         logger,
	}

	// 欧意配置参数
	okDiffParams := DiffQueryParams{
		DiffRdb:        hkRdb,
		DiffTable:      config.OkDiffTable,
		LeftMetaTable:  config.OkFuturesMetaInfo,
		RightMetaTable: config.OkSpotMetaInfo,
		Negative:       false,
		MetaRdb:        metaRdb,
		Logger:         logger,
	}

	// 币安配置参数
	bnDiffParams := DiffQueryParams{
		DiffRdb:        tokyoRdb,
		DiffTable:      config.BnDiffTable,
		LeftMetaTable:  config.BnFuturesMetaInfo,
		RightMetaTable: config.BnSpotMetaInfo,
		Negative:       false,
		MetaRdb:        metaRdb,
		Logger:         logger,
	}

	// 执行查询
	positiveCrossDiffSymbols := getRedisDiff(crossDiffPositiveParams)
	negativeCrossDiffSymbols := getRedisDiff(crossDiffNegativeParams)
	OkDiffSymbols := getRedisDiff(okDiffParams)
	BnDiffSymbols := getRedisDiff(bnDiffParams)

	// 第一次筛选：价差大于0.5%的交易对，合并通知
	filteredCrossPositive := filterSymbols(positiveCrossDiffSymbols, DiffLowPeriodFilter)
	filteredCrossNegative := filterSymbols(negativeCrossDiffSymbols, DiffLowPeriodFilter)
	filteredOkDiff := filterSymbols(OkDiffSymbols, DiffLowFilter)
	filteredBnDiff := filterSymbols(BnDiffSymbols, DiffLowFilter)

	// 合并所有大价差信息
	var allDiffAlerts []string

	if len(filteredCrossPositive) != 0 {
		symbolDetails := formatSymbolsForAlert(filteredCrossPositive)
		allDiffAlerts = append(allDiffAlerts, fmt.Sprintf("跨所(bn-ok): %s", symbolDetails))
	}

	if len(filteredCrossNegative) != 0 {
		symbolDetails := formatSymbolsForAlert(filteredCrossNegative)
		allDiffAlerts = append(allDiffAlerts, fmt.Sprintf("跨所(ok-bn): %s", symbolDetails))
	}

	if len(filteredOkDiff) != 0 {
		symbolDetails := formatSymbolsForAlert(filteredOkDiff)
		allDiffAlerts = append(allDiffAlerts, fmt.Sprintf("Okex: %s", symbolDetails))
	}

	if len(filteredBnDiff) != 0 {
		symbolDetails := formatSymbolsForAlert(filteredBnDiff)
		allDiffAlerts = append(allDiffAlerts, fmt.Sprintf("Binance: %s", symbolDetails))
	}

	// 如果有任何大价差机会，合并发送一条通知
	if len(allDiffAlerts) > 0 {
		content := fmt.Sprintf("大价差机会：%s", strings.Join(allDiffAlerts, "; "))
		logger.Info(content)
	}

	// 第二次筛选：价差大于2.5%, 电话通知
	phoneAlertCrossPositive := filterSymbols(positiveCrossDiffSymbols, DiffHighPeriodFilter)
	phoneAlertCrossNegative := filterSymbols(negativeCrossDiffSymbols, DiffHighPeriodFilter)
	phoneAlertOkDiff := filterSymbols(OkDiffSymbols, DiffHighFilter)
	phoneAlertBnDiff := filterSymbols(BnDiffSymbols, DiffHighFilter)

	// 合并所有电话通知信息
	var allPhoneAlerts []string

	if len(phoneAlertCrossPositive) != 0 {
		symbolDetails := formatSymbolsForAlert(phoneAlertCrossPositive)
		allPhoneAlerts = append(allPhoneAlerts, fmt.Sprintf("跨所(bn-ok): %s", symbolDetails))
	}

	if len(phoneAlertCrossNegative) != 0 {
		symbolDetails := formatSymbolsForAlert(phoneAlertCrossNegative)
		allPhoneAlerts = append(allPhoneAlerts, fmt.Sprintf("跨所(ok-bn): %s", symbolDetails))
	}

	if len(phoneAlertOkDiff) != 0 {
		symbolDetails := formatSymbolsForAlert(phoneAlertOkDiff)
		allPhoneAlerts = append(allPhoneAlerts, fmt.Sprintf("Okex: %s", symbolDetails))
	}

	if len(phoneAlertBnDiff) != 0 {
		symbolDetails := formatSymbolsForAlert(phoneAlertBnDiff)
		allPhoneAlerts = append(allPhoneAlerts, fmt.Sprintf("Binance: %s", symbolDetails))
	}

	// 如果有任何电话通知，合并发送一条
	if len(allPhoneAlerts) > 0 {
		content := fmt.Sprintf("超大价差机会：%s", strings.Join(allPhoneAlerts, "; "))
		logger.Info(content)
		alertClient.SendUniqueAlert(content, utils.ChannelArbitrage, "diffnotifyTest3", 60, utils.UserAll)
	}

}
