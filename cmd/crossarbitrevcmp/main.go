package main

import (
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/logs"
	task "GoTrader/task/arbit"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	taskName := "CrossArbitRevCmp"

	leftAccountName := "testLiquid"
	rightAccountName := "TestLiquidCostom"

	// 阈值
	keepAvailable := 50.0

	logDir := config.TestLogDir

	// 代理
	proxy := true

	// 检查logger
	bndataWriter := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, taskName, "bndata.log"), // 日志文件的位置
		MaxSize:   100,                                           // 文件最大尺寸（以MB为单位）
		MaxAge:    3,                                             // 保留旧文件的最大天数
		Compress:  true,                                          // 是否压缩/归档旧文件
		LocalTime: true,                                          // 使用本地时间创建时间戳
	}
	bnDataLogger := slog.New(slog.NewJSONHandler(
		bndataWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	okdataWriter := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, taskName, "okdata.log"), // 日志文件的位置
		MaxSize:   100,                                           // 文件最大尺寸（以MB为单位）
		MaxAge:    3,                                             // 保留旧文件的最大天数
		Compress:  true,                                          // 是否压缩/归档旧文件
		LocalTime: true,                                          // 使用本地时间创建时间戳
	}
	okDataLogger := slog.New(slog.NewJSONHandler(
		okdataWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	marketWriter := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, taskName, "market.log"), // 日志文件的位置
		MaxSize:   100,                                           // 文件最大尺寸（以MB为单位）
		MaxAge:    3,                                             // 保留旧文件的最大天数
		Compress:  true,                                          // 是否压缩/归档旧文件
		LocalTime: true,                                          // 使用本地时间创建时间戳
	}
	marketLogger := slog.New(slog.NewJSONHandler(
		marketWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	brokerWriter := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, taskName, "broker.log"), // 日志文件的位置
		MaxSize:   100,                                           // 文件最大尺寸（以MB为单位）
		MaxAge:    3,                                             // 保留旧文件的最大天数
		Compress:  true,                                          // 是否压缩/归档旧文件
		LocalTime: true,                                          // 使用本地时间创建时间戳
	}
	brokerLogger := slog.New(slog.NewJSONHandler(
		brokerWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	clientPath := filepath.Join(logDir, taskName, "client.log")
	clientFileWriter := &lumberjack.Logger{
		Filename:  clientPath, // 日志文件的位置
		MaxSize:   100,        // 文件最大尺寸（以MB为单位）
		MaxAge:    3,          // 保留旧文件的最大天数
		Compress:  true,       // 是否压缩/归档旧文件
		LocalTime: true,       // 使用本地时间创建时间戳
	}
	defer clientFileWriter.Close()

	taskPath := filepath.Join(logDir, taskName, "task.log")
	taskFileWriter := &lumberjack.Logger{
		Filename:  taskPath, // 日志文件的位置
		MaxSize:   100,      // 文件最大尺寸（以MB为单位）
		MaxAge:    3,        // 保留旧文件的最大天数
		Compress:  true,     // 是否压缩/归档旧文件
		LocalTime: true,     // 使用本地时间创建时间戳
	}
	defer taskFileWriter.Close()

	testFeishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		taskName,
		slog.LevelWarn,
	)

	notifyFeishuWriter := logs.NewFeishuWriter(
		[]string{},
		config.GroupZeroIgnore.Hook,
		config.GroupZeroIgnore.Secret,
		taskName,
		slog.LevelWarn,
	)

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(testFeishuWriter, taskFileWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// 通知log
	notifyLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(clientFileWriter, notifyFeishuWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// client logger
	clientLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(clientFileWriter, testFeishuWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	taskLogger.Info("Starting cross arbitrage task...")

	// 取消信号接收channel
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 定时器
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	// ctx
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.TaskDB,
	})

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			taskLogger.Error("Recovered from panic:", "panic", r)
			taskLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	// 帐号
	leftAccount := ok.OkAccounts[leftAccountName]
	rightAccount := bn.BnAccounts[rightAccountName]

	// 创建 Broker 对象
	okb := broker.NewBroker(leftAccount["API_KEY"], leftAccount["API_SECRET"], leftAccount["PASSPHRASE"], leftAccount["DB_INDEX"], broker.Portfolio, brokerLogger.With("broker", "ok"))
	bnb := broker.NewBroker(rightAccount["API_KEY"], rightAccount["API_SECRET"], rightAccount["PASSPHRASE"], rightAccount["DB_INDEX"], broker.Costom, brokerLogger.With("broker", "bn"))

	// 创建客户端
	bndc, bnac, okdc, okac, err := factory.NewCrossClients(ctx, bnb, okb, proxy, clientLogger, bnDataLogger, okDataLogger, marketLogger)

	if err != nil {
		taskLogger.Error("NewCrossClients failed", "error", err)
		return
	}
	task.RunCrossArbitTasks(
		ticker,
		signalChan,
		rdb,
		config.ArbitTaskTable,
		config.InfoTableCmp,
		logDir,
		taskName,
		"OK",
		"BN",
		keepAvailable,

		okac,
		okdc,
		okb,

		bnac,
		bndc,
		bnb,

		testFeishuWriter,
		taskLogger,
		notifyLogger,
	)
}
