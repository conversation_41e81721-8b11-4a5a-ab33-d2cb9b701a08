package main

import (
	config "GoTrader/internal"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"maps"
	"time"

	"github.com/redis/go-redis/v9"
)

// 检查套利任务是否正常运行
func checkArbitTasks(rdb *redis.Client, logger *slog.Logger) (noRunTasks []string, delayTasks []string) {
	return checkArbitTasksRunning(rdb, []string{config.ArbitTaskTable, config.BnCrossFundTaskTable, config.BnCrossDiffTaskTable}, config.InfoTable, logger)
}

// 检查套利任务是否正常运行
func checkArbitTasksRunning(rdb *redis.Client, taskTables []string, taskInfoTable string, logger *slog.Logger) (noRunTasks []string, delayTasks []string) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	// 监控套利运行状态
	// 目前的任务
	arbitTasks := map[string]string{}
	for _, taskTable := range taskTables {
		tasks, err := rdb.HGetAll(ctx, taskTable).Result()
		if err != nil {
			logger.Info("获取套利任务失败", "error", err)
			return noRunTasks, delayTasks
		}
		maps.Copy(arbitTasks, tasks)
	}

	// 当前任务运行状态
	currTasks, err := rdb.HGetAll(ctx, taskInfoTable).Result()
	if err != nil {
		logger.Info("获取当前任务运行状态失败", "error", err)
		return noRunTasks, delayTasks
	}

	// 活跃的任务列表
	activeTasks := make([]string, 0)
	for k, v := range arbitTasks {
		// 将json字符串转换为map
		var params map[string]string
		json.Unmarshal([]byte(v), &params)
		if params["S"] == "active" {
			activeTasks = append(activeTasks, k)
		}
	}

	// 未运行的任务
	noRunTasks = make([]string, 0)
	// 更新延迟的任务
	delayTasks = make([]string, 0)
	for _, task := range activeTasks {
		infoJson, ok := currTasks[task]
		if !ok {
			noRunTasks = append(noRunTasks, task)
		} else {
			// 检查任务是否过期
			var info map[string]string
			json.Unmarshal([]byte(infoJson), &info)
			ts, err := time.Parse("2006-01-02 15:04:05.000 MST", info["time"])
			if err != nil {
				fmt.Println(err)
			}
			// 时间戳比当前时间晚5m
			if ts.Add(5 * time.Minute).Before(time.Now()) {
				delayTasks = append(delayTasks, task)
			}
		}
	}
	return noRunTasks, delayTasks
}
